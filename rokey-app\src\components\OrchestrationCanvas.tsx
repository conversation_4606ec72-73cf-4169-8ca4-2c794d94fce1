'use client';

import React, { useState, useEffect } from 'react';
import { useOrchestrationStream } from '@/hooks/useOrchestrationStream';
import { OrchestrationChatroom } from './OrchestrationChatroom';
import { 
  XMarkIcon,
  MinusIcon,
  ChatBubbleLeftRightIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface OrchestrationCanvasProps {
  executionId: string;
  onComplete?: (result: string) => void;
  onError?: (error: string) => void;
}

export const OrchestrationCanvas: React.FC<OrchestrationCanvasProps> = ({
  executionId,
  onComplete,
  onError
}) => {
  const [isCanvasOpen, setIsCanvasOpen] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);
  const [orchestrationComplete, setOrchestrationComplete] = useState(false);
  const [finalResult, setFinalResult] = useState<string>('');

  const { events, isConnected, error } = useOrchestrationStream(executionId);

  // Handle orchestration completion
  useEffect(() => {
    const synthesisCompleteEvent = events.find(event => event.type === 'synthesis_complete');
    if (synthesisCompleteEvent && !orchestrationComplete) {
      setOrchestrationComplete(true);
      const result = synthesisCompleteEvent.data?.result || 'Orchestration completed successfully';
      setFinalResult(result);
      
      // Notify parent component
      if (onComplete) {
        onComplete(result);
      }
    }
  }, [events, orchestrationComplete, onComplete]);

  // Handle errors
  useEffect(() => {
    if (error && onError) {
      onError(error);
    }
  }, [error, onError]);

  const handleMinimize = () => {
    setIsMinimized(true);
    setIsCanvasOpen(false);
  };

  const handleMaximize = () => {
    setIsMinimized(false);
    setIsCanvasOpen(true);
  };

  const handleClose = () => {
    setIsCanvasOpen(false);
    setIsMinimized(false);
  };

  // Minimized card state
  if (isMinimized) {
    return (
      <div className="fixed bottom-6 left-6 z-50">
        <div 
          onClick={handleMaximize}
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-xl shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 min-w-[280px]"
        >
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <ChatBubbleLeftRightIcon className="w-6 h-6" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm">AI Team Collaboration</h3>
              <p className="text-xs opacity-90">
                {orchestrationComplete ? 'Completed' : 'Multi-Role Orchestration'}
              </p>
            </div>
            <div className="flex-shrink-0">
              {!orchestrationComplete && (
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
              )}
              {orchestrationComplete && (
                <SparklesIcon className="w-5 h-5" />
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Canvas is closed
  if (!isCanvasOpen) {
    return null;
  }

  // Debug log when rendering
  console.log('🎭 [DEBUG] OrchestrationCanvas is rendering!', {
    isCanvasOpen,
    isMinimized,
    executionId,
    shouldBeVisible: isCanvasOpen && !isMinimized,
    transformClass: isCanvasOpen ? 'translate-x-0' : 'translate-x-full'
  });

  return (
    <>
      {/* Canvas Overlay */}
      <div className="fixed inset-0 z-[9998] bg-black/20 backdrop-blur-sm" onClick={handleClose} />

      {/* Canvas Panel */}
      <div className={`fixed top-0 right-0 h-full w-1/2 bg-white shadow-2xl z-[9999] transform transition-transform duration-300 ease-out border-4 border-red-500 ${
        isCanvasOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        {/* Debug indicator */}
        <div className="absolute top-4 left-4 bg-red-500 text-white p-2 rounded z-[10000] text-xs font-bold">
          CANVAS VISIBLE - isOpen: {isCanvasOpen.toString()}
        </div>
        {/* Canvas Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <ChatBubbleLeftRightIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="font-semibold text-gray-900">AI Team Collaboration</h2>
              <p className="text-xs text-gray-600">
                {orchestrationComplete ? 'Orchestration Complete' : 'Multi-Role Orchestration in Progress'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleMinimize}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
              aria-label="Minimize canvas"
            >
              <MinusIcon className="w-4 h-4" />
            </button>
            <button
              onClick={handleClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
              aria-label="Close canvas"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Canvas Content */}
        <div className="flex-1 h-full overflow-hidden">
          <OrchestrationChatroom
            executionId={executionId}
            events={events}
            isConnected={isConnected}
            error={error}
            isComplete={orchestrationComplete}
          />
        </div>
      </div>
    </>
  );
};
