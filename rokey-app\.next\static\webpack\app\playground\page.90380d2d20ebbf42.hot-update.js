"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationChatroom.tsx":
/*!**************************************************!*\
  !*** ./src/components/OrchestrationChatroom.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationChatroom: function() { return /* binding */ OrchestrationChatroom; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatMessage */ \"(app-pages-browser)/./src/components/ChatMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* harmony import */ var _utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/orchestrationUtils */ \"(app-pages-browser)/./src/utils/orchestrationUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationChatroom auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationChatroom = (param)=>{\n    let { executionId, events, isConnected, error, isComplete } = param;\n    _s();\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingSpecialists, setTypingSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDFAD [CHATROOM DEBUG] State:\", {\n            executionId,\n            eventsCount: events.length,\n            isConnected,\n            error,\n            isComplete,\n            events: events.slice(0, 3) // Show first 3 events\n        });\n    }, [\n        executionId,\n        events,\n        isConnected,\n        error,\n        isComplete\n    ]);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        chatMessages\n    ]);\n    // Add simulated interactive events for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (events.length > 0 && !chatMessages.some((msg)=>msg.type === \"clarification\")) {\n            // Add some simulated clarification events for testing\n            const simulatedEvents = [\n                ...events,\n                // Add a clarification question after the first assignment\n                {\n                    type: \"specialist_clarification\",\n                    role_id: \"game-designer\",\n                    data: {\n                        message: null\n                    },\n                    timestamp: new Date(Date.now() + 2000).toISOString()\n                }\n            ];\n            // Use simulated events for now to test the enhanced conversations\n            processEvents(simulatedEvents);\n        } else {\n            processEvents(events);\n        }\n    }, [\n        events,\n        executionId\n    ]);\n    // Convert orchestration events to chat messages\n    const processEvents = (eventsToProcess)=>{\n        const newMessages = [];\n        const currentlyTyping = new Set();\n        eventsToProcess.forEach((event, index)=>{\n            const timestamp = new Date(event.timestamp || Date.now());\n            const messageId = \"\".concat(executionId, \"-\").concat(index);\n            switch(event.type){\n                case \"orchestration_started\":\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"orchestration_started\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"moderator_introduction\":\n                    var _event_data;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.message) || \"Hello team! Let me introduce everyone...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"specialist_introduction\":\n                    var _event_data1, _event_data2;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.message) || \"Introducing \".concat((_event_data2 = event.data) === null || _event_data2 === void 0 ? void 0 : _event_data2.specialist, \" specialist...\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"moderator_assignment\":\n                    var _event_data3, _event_data4;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data3 = event.data) === null || _event_data3 === void 0 ? void 0 : _event_data3.message) || \"Assignment given to \".concat((_event_data4 = event.data) === null || _event_data4 === void 0 ? void 0 : _event_data4.target_specialist),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"task_decomposed\":\n                    var _event_data5;\n                    const steps = ((_event_data5 = event.data) === null || _event_data5 === void 0 ? void 0 : _event_data5.steps) || [];\n                    const teamIntro = steps.map((step)=>\"\\uD83E\\uDD16 @\".concat(step.roleId, \" - \").concat(step.modelName || \"AI Specialist\")).join(\"\\n\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: \"\".concat((0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"task_decomposed\"), \"\\n\\n\").concat(teamIntro, \"\\n\\nLet's begin the collaboration!\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"step_assigned\":\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateModeratorClarification)(event.role_id || \"specialist\", \"\", \"assignment\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"moderator_assignment\":\n                    var _event_data6;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: ((_event_data6 = event.data) === null || _event_data6 === void 0 ? void 0 : _event_data6.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"moderator_assignment\", event.role_id),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"specialist_acknowledgment\":\n                    var _event_data7;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: ((_event_data7 = event.data) === null || _event_data7 === void 0 ? void 0 : _event_data7.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateSpecialistPersonalityMessage)(event.role_id || \"specialist\", \"acknowledgment\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"specialist_clarification\":\n                    var _event_data8;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: ((_event_data8 = event.data) === null || _event_data8 === void 0 ? void 0 : _event_data8.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateSpecialistPersonalityMessage)(event.role_id || \"specialist\", \"clarification\"),\n                        timestamp,\n                        type: \"clarification\"\n                    });\n                    break;\n                case \"specialist_working\":\n                    var _event_data_speaker, _event_data9, _event_data10, _event_data11, _event_data12;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: ((_event_data9 = event.data) === null || _event_data9 === void 0 ? void 0 : (_event_data_speaker = _event_data9.speaker) === null || _event_data_speaker === void 0 ? void 0 : _event_data_speaker.replace(\"_\", \" \").toUpperCase()) || \"Specialist\",\n                        roleId: (_event_data10 = event.data) === null || _event_data10 === void 0 ? void 0 : _event_data10.speaker,\n                        content: ((_event_data11 = event.data) === null || _event_data11 === void 0 ? void 0 : _event_data11.message) || \"\\uD83D\\uDD04 Analyzing the requirements...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    // Add to typing indicators\n                    if ((_event_data12 = event.data) === null || _event_data12 === void 0 ? void 0 : _event_data12.speaker) {\n                        currentlyTyping.add(event.data.speaker);\n                    }\n                    break;\n                case \"specialist_response\":\n                    var _event_data_speaker1, _event_data13, _event_data14, _event_data15, _event_data16;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: ((_event_data13 = event.data) === null || _event_data13 === void 0 ? void 0 : (_event_data_speaker1 = _event_data13.speaker) === null || _event_data_speaker1 === void 0 ? void 0 : _event_data_speaker1.replace(\"_\", \" \").toUpperCase()) || \"Specialist\",\n                        roleId: (_event_data14 = event.data) === null || _event_data14 === void 0 ? void 0 : _event_data14.speaker,\n                        content: ((_event_data15 = event.data) === null || _event_data15 === void 0 ? void 0 : _event_data15.message) || \"Task completed successfully!\",\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    // Remove from typing indicators\n                    if ((_event_data16 = event.data) === null || _event_data16 === void 0 ? void 0 : _event_data16.speaker) {\n                        currentlyTyping.delete(event.data.speaker);\n                    }\n                    break;\n                case \"moderator_handoff\":\n                    var _event_data17;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data17 = event.data) === null || _event_data17 === void 0 ? void 0 : _event_data17.message) || \"Handing off to next specialist...\",\n                        timestamp,\n                        type: \"handoff\"\n                    });\n                    break;\n                case \"moderator_synthesis_start\":\n                    var _event_data18;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data18 = event.data) === null || _event_data18 === void 0 ? void 0 : _event_data18.message) || \"Starting synthesis of all specialist contributions...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    currentlyTyping.add(\"moderator\");\n                    break;\n                case \"synthesis_progress\":\n                    var _event_data19;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data19 = event.data) === null || _event_data19 === void 0 ? void 0 : _event_data19.message) || \"\\uD83E\\uDDE9 Weaving together specialist insights...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"orchestration_complete\":\n                    var _event_data20;\n                    currentlyTyping.delete(\"moderator\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data20 = event.data) === null || _event_data20 === void 0 ? void 0 : _event_data20.message) || \"\\uD83C\\uDF89 Team collaboration complete!\",\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n                case \"step_started\":\n                    // Add to typing indicators\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"step_progress\":\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"specialist_message\":\n                    var _event_data21, _event_data22;\n                    const completionMessage = ((_event_data21 = event.data) === null || _event_data21 === void 0 ? void 0 : _event_data21.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateSpecialistPersonalityMessage)(event.role_id || \"specialist\", \"completion\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: \"\".concat(completionMessage, \"\\n\\n\").concat(((_event_data22 = event.data) === null || _event_data22 === void 0 ? void 0 : _event_data22.output) || \"Task completed successfully!\"),\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n                case \"step_completed\":\n                    // Remove from typing\n                    if (event.role_id) {\n                        currentlyTyping.delete(event.role_id);\n                    }\n                    break;\n                case \"handoff_message\":\n                    var _event_data23, _event_data24;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data23 = event.data) === null || _event_data23 === void 0 ? void 0 : _event_data23.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"handoff_message\", (_event_data24 = event.data) === null || _event_data24 === void 0 ? void 0 : _event_data24.fromRole),\n                        timestamp,\n                        type: \"handoff\"\n                    });\n                    break;\n                case \"synthesis_started\":\n                    var _event_data25;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data25 = event.data) === null || _event_data25 === void 0 ? void 0 : _event_data25.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"synthesis_started\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    currentlyTyping.add(\"moderator\");\n                    break;\n                case \"synthesis_complete\":\n                    var _event_data26;\n                    currentlyTyping.delete(\"moderator\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data26 = event.data) === null || _event_data26 === void 0 ? void 0 : _event_data26.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"synthesis_complete\"),\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n            }\n        });\n        setChatMessages(newMessages);\n        setTypingSpecialists(currentlyTyping);\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 361,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n            lineNumber: 360,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 text-xs font-medium \".concat(isConnected ? \"bg-green-50 text-green-700 border-b border-green-100\" : \"bg-yellow-50 text-yellow-700 border-b border-yellow-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? \"bg-green-500\" : \"bg-yellow-500\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: isConnected ? \"Connected to AI Team\" : \"Connecting...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    chatMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600 animate-pulse\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Waiting for AI team to start collaboration...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, undefined),\n                    chatMessages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from(typingSpecialists).map((specialist)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_3__.TypingIndicator, {\n                            senderName: specialist,\n                            roleId: specialist !== \"moderator\" ? specialist : undefined\n                        }, specialist, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrchestrationChatroom, \"xNWPH4WkId/aBek//nEKgqbFhaw=\");\n_c = OrchestrationChatroom;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationChatroom\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\n"));

/***/ })

});