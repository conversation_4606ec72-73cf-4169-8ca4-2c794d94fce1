"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationChatroom.tsx":
/*!**************************************************!*\
  !*** ./src/components/OrchestrationChatroom.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationChatroom: function() { return /* binding */ OrchestrationChatroom; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatMessage */ \"(app-pages-browser)/./src/components/ChatMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* harmony import */ var _utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/orchestrationUtils */ \"(app-pages-browser)/./src/utils/orchestrationUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationChatroom auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationChatroom = (param)=>{\n    let { executionId, events, isConnected, error, isComplete } = param;\n    _s();\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingSpecialists, setTypingSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        chatMessages\n    ]);\n    // Convert orchestration events to chat messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const newMessages = [];\n        const currentlyTyping = new Set();\n        events.forEach((event, index)=>{\n            const timestamp = new Date(event.timestamp || Date.now());\n            const messageId = \"\".concat(executionId, \"-\").concat(index);\n            switch(event.type){\n                case \"orchestration_started\":\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"orchestration_started\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"task_decomposed\":\n                    var _event_data;\n                    const steps = ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.steps) || [];\n                    const teamIntro = steps.map((step)=>\"\\uD83E\\uDD16 @\".concat(step.roleId, \" - \").concat(step.modelName || \"AI Specialist\")).join(\"\\n\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: \"\\uD83D\\uDCCB I've analyzed the task and assembled this expert team:\\n\\n\".concat(teamIntro, \"\\n\\nLet's begin the collaboration!\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"step_assigned\":\n                    var _event_data1;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: \"\\uD83C\\uDFAF @\".concat(event.role_id, \", you're up! \").concat(((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.commentary) || \"Please begin your specialized work on this task.\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"moderator_assignment\":\n                    var _event_data2;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: ((_event_data2 = event.data) === null || _event_data2 === void 0 ? void 0 : _event_data2.message) || \"\\uD83C\\uDFAF @\".concat(event.role_id, \", you're up! Please begin your specialized work on this task.\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"specialist_acknowledgment\":\n                    var _event_data3;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: ((_event_data3 = event.data) === null || _event_data3 === void 0 ? void 0 : _event_data3.message) || \"✅ Understood! I'm \".concat(event.role_id, \" and I'll handle this task with expertise. Starting work now...\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"step_started\":\n                    // Add to typing indicators\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"step_progress\":\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"specialist_message\":\n                    var _event_data4, _event_data5;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: \"\".concat(((_event_data4 = event.data) === null || _event_data4 === void 0 ? void 0 : _event_data4.message) || \"\\uD83C\\uDF89 Perfect! I've completed my part of the task. Here's what I've delivered:\", \"\\n\\n\").concat(((_event_data5 = event.data) === null || _event_data5 === void 0 ? void 0 : _event_data5.output) || \"Task completed successfully!\"),\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n                case \"step_completed\":\n                    // Remove from typing\n                    if (event.role_id) {\n                        currentlyTyping.delete(event.role_id);\n                    }\n                    break;\n                case \"handoff_message\":\n                    var _event_data6, _event_data7, _event_data8;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data6 = event.data) === null || _event_data6 === void 0 ? void 0 : _event_data6.message) || \"✨ Excellent work, @\".concat((_event_data7 = event.data) === null || _event_data7 === void 0 ? void 0 : _event_data7.fromRole, \"! Quality looks great. Now passing to @\").concat((_event_data8 = event.data) === null || _event_data8 === void 0 ? void 0 : _event_data8.toRole, \"...\"),\n                        timestamp,\n                        type: \"handoff\"\n                    });\n                    break;\n                case \"synthesis_started\":\n                    var _event_data9;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data9 = event.data) === null || _event_data9 === void 0 ? void 0 : _event_data9.message) || \"\\uD83E\\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    currentlyTyping.add(\"moderator\");\n                    break;\n                case \"synthesis_complete\":\n                    var _event_data10;\n                    currentlyTyping.delete(\"moderator\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data10 = event.data) === null || _event_data10 === void 0 ? void 0 : _event_data10.message) || \"\\uD83C\\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!\",\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n            }\n        });\n        setChatMessages(newMessages);\n        setTypingSpecialists(currentlyTyping);\n    }, [\n        events,\n        executionId\n    ]);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 text-xs font-medium \".concat(isConnected ? \"bg-green-50 text-green-700 border-b border-green-100\" : \"bg-yellow-50 text-yellow-700 border-b border-yellow-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? \"bg-green-500\" : \"bg-yellow-500\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: isConnected ? \"Connected to AI Team\" : \"Connecting...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    chatMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600 animate-pulse\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Waiting for AI team to start collaboration...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, undefined),\n                    chatMessages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from(typingSpecialists).map((specialist)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_3__.TypingIndicator, {\n                            senderName: specialist,\n                            roleId: specialist !== \"moderator\" ? specialist : undefined\n                        }, specialist, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrchestrationChatroom, \"Trlsk+ImATjahibYSefjX0C5OX4=\");\n_c = OrchestrationChatroom;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationChatroom\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/orchestrationUtils.ts":
/*!*****************************************!*\
  !*** ./src/utils/orchestrationUtils.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateExecutionStrategy: function() { return /* binding */ calculateExecutionStrategy; },\n/* harmony export */   createOrchestrationEventStream: function() { return /* binding */ createOrchestrationEventStream; },\n/* harmony export */   decomposeTaskWithDependencies: function() { return /* binding */ decomposeTaskWithDependencies; },\n/* harmony export */   emitOrchestrationEvent: function() { return /* binding */ emitOrchestrationEvent; },\n/* harmony export */   generateConversationalMessage: function() { return /* binding */ generateConversationalMessage; },\n/* harmony export */   generateModeratorClarification: function() { return /* binding */ generateModeratorClarification; },\n/* harmony export */   generateModeratorCommentary: function() { return /* binding */ generateModeratorCommentary; },\n/* harmony export */   generateSpecialistPersonalityMessage: function() { return /* binding */ generateSpecialistPersonalityMessage; }\n/* harmony export */ });\n// Enhanced orchestration utilities for multi-role AI team collaboration\n// Create a Server-Sent Events stream for orchestration updates\nfunction createOrchestrationEventStream(executionId) {\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        start (controller) {\n            // Send initial connection event\n            const initialEvent = {\n                id: crypto.randomUUID(),\n                execution_id: executionId,\n                type: \"orchestration_started\",\n                timestamp: new Date().toISOString(),\n                data: {\n                    message: \"Orchestration stream connected\"\n                }\n            };\n            const eventData = \"data: \".concat(JSON.stringify(initialEvent), \"\\n\\n\");\n            controller.enqueue(encoder.encode(eventData));\n        },\n        cancel () {\n            console.log(\"[Orchestration Stream] Stream cancelled for execution \".concat(executionId));\n        }\n    });\n}\n// Enhanced task decomposition with dependency analysis\nasync function decomposeTaskWithDependencies(originalPrompt, roles, classificationApiKey) {\n    // Create system prompt for intelligent task decomposition\n    const decompositionPrompt = 'You are an expert AI orchestrator. Analyze this request and create an optimal workflow for multiple AI specialists.\\n\\nOriginal Request: \"'.concat(originalPrompt, '\"\\n\\nAvailable Roles: ').concat(roles.map((r)=>\"\".concat(r.roleId, \" (confidence: \").concat(r.confidence, \")\")).join(\", \"), '\\n\\nCreate a detailed workflow with:\\n1. Task breakdown into specific steps\\n2. Dependencies between steps\\n3. Opportunities for parallel processing\\n4. Estimated complexity and duration\\n5. Clear handoff instructions between models\\n\\nRespond in JSON format:\\n{\\n  \"workflow\": {\\n    \"steps\": [\\n      {\\n        \"stepNumber\": 1,\\n        \"roleId\": \"role_name\",\\n        \"prompt\": \"specific task for this role\",\\n        \"dependencies\": [],\\n        \"canRunInParallel\": false,\\n        \"priority\": 1,\\n        \"estimatedDuration\": 30000,\\n        \"moderatorInstructions\": \"how to validate and hand off results\"\\n      }\\n    ],\\n    \"parallelGroups\": [[1], [2, 3], [4]],\\n    \"totalEstimatedDuration\": 120000,\\n    \"complexityScore\": 7\\n  },\\n  \"reasoning\": \"explanation of the workflow design\"\\n}');\n    try {\n        var _result_choices__message, _result_choices_, _result_choices;\n        const response = await fetch(\"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(classificationApiKey)\n            },\n            body: JSON.stringify({\n                model: \"gemini-2.0-flash-lite\",\n                messages: [\n                    {\n                        role: \"system\",\n                        content: \"You are an expert AI workflow orchestrator.\"\n                    },\n                    {\n                        role: \"user\",\n                        content: decompositionPrompt\n                    }\n                ],\n                temperature: 0.3,\n                max_tokens: 1500,\n                response_format: {\n                    type: \"json_object\"\n                }\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Decomposition API error: \".concat(response.status));\n        }\n        const result = await response.json();\n        const content = (_result_choices = result.choices) === null || _result_choices === void 0 ? void 0 : (_result_choices_ = _result_choices[0]) === null || _result_choices_ === void 0 ? void 0 : (_result_choices__message = _result_choices_.message) === null || _result_choices__message === void 0 ? void 0 : _result_choices__message.content;\n        if (!content) {\n            throw new Error(\"Empty decomposition response\");\n        }\n        const parsed = JSON.parse(content);\n        return parsed.workflow;\n    } catch (error) {\n        console.warn(\"[Task Decomposition] Error: \".concat(error, \", falling back to simple decomposition\"));\n        // Fallback to simple sequential decomposition\n        return createFallbackWorkflow(originalPrompt, roles);\n    }\n}\n// Fallback workflow creation for when AI decomposition fails\nfunction createFallbackWorkflow(originalPrompt, roles) {\n    const sortedRoles = [\n        ...roles\n    ].sort((a, b)=>a.executionOrder - b.executionOrder);\n    const steps = sortedRoles.map((role, index)=>({\n            stepNumber: index + 1,\n            roleId: role.roleId,\n            prompt: index === 0 ? \"Handle the \".concat(role.roleId, ' aspect of this request: \"').concat(originalPrompt, '\"') : \"Continue with the \".concat(role.roleId, \" aspect based on the previous step's output: {{previousOutput}}\"),\n            dependencies: index === 0 ? [] : [\n                index\n            ],\n            canRunInParallel: false,\n            priority: index + 1,\n            estimatedDuration: 45000,\n            moderatorInstructions: \"Validate the \".concat(role.roleId, \" output and prepare for next step\")\n        }));\n    return {\n        steps,\n        parallelGroups: steps.map((_, index)=>[\n                index + 1\n            ]),\n        totalEstimatedDuration: steps.length * 45000,\n        complexityScore: Math.min(steps.length * 2, 10)\n    };\n}\n// Emit orchestration event to stream\nasync function emitOrchestrationEvent(executionId, eventType, data, stepNumber, roleId, modelName) {\n    const event = {\n        id: crypto.randomUUID(),\n        execution_id: executionId,\n        type: eventType,\n        timestamp: new Date().toISOString(),\n        data,\n        step_number: stepNumber,\n        role_id: roleId,\n        model_name: modelName\n    };\n    // Store event in database for persistence and replay\n    try {\n        // This would be implemented with your Supabase client\n        console.log(\"[Orchestration Event] \".concat(eventType, \":\"), event);\n    // In a real implementation, you'd store this in the orchestration_events table\n    // and broadcast to connected clients via WebSocket or SSE\n    } catch (error) {\n        console.error(\"[Orchestration Event] Failed to emit \".concat(eventType, \":\"), error);\n    }\n}\n// Generate moderator commentary for entertainment value\nfunction generateModeratorCommentary(eventType, stepData, roleId) {\n    const commentaries = {\n        orchestration_started: [\n            \"\\uD83C\\uDFAC Alright team, we've got an interesting challenge ahead!\",\n            \"\\uD83D\\uDE80 Let's break this down and see who's best suited for each part.\",\n            \"\\uD83C\\uDFAF Time to coordinate our AI specialists for optimal results.\"\n        ],\n        step_assigned: [\n            \"\\uD83D\\uDCCB Assigning the \".concat(roleId, \" specialist to handle this part.\"),\n            \"\\uD83C\\uDFAA Our \".concat(roleId, \" expert is stepping up to the plate!\"),\n            \"⚡ Perfect match - \".concat(roleId, \" is exactly what we need here.\")\n        ],\n        step_started: [\n            \"\\uD83D\\uDD25 \".concat(roleId, \" is now working their magic...\"),\n            \"⚙️ Watch \".concat(roleId, \" tackle this challenge in real-time!\"),\n            \"\\uD83C\\uDFA8 \".concat(roleId, \" is crafting something special for us.\")\n        ],\n        step_completed: [\n            \"✅ Excellent work from \".concat(roleId, \"! Moving to the next phase.\"),\n            \"\\uD83C\\uDF89 \".concat(roleId, \" delivered exactly what we needed. Handoff time!\"),\n            \"\\uD83D\\uDCAB Beautiful execution by \".concat(roleId, \". The team is flowing perfectly.\")\n        ],\n        synthesis_started: [\n            \"\\uD83E\\uDDE9 Now I'm weaving all these pieces together...\",\n            \"\\uD83C\\uDFAD Time for the grand finale - combining all our specialists' work!\",\n            \"\\uD83C\\uDF1F Watch as I synthesize these brilliant contributions into one cohesive result.\"\n        ]\n    };\n    const options = commentaries[eventType] || [\n        \"\\uD83E\\uDD16 Processing...\"\n    ];\n    return options[Math.floor(Math.random() * options.length)];\n}\n// Generate conversational messages for chatroom experience\nfunction generateConversationalMessage(eventType, roleId, context) {\n    const messages = {\n        orchestration_started: [\n            \"\\uD83C\\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.\",\n            \"\\uD83D\\uDE80 Alright everyone, we've got an exciting challenge ahead. Let me coordinate our specialists.\",\n            \"\\uD83C\\uDFAF Time to bring together our expert AI team for optimal results!\"\n        ],\n        task_decomposed: [\n            \"\\uD83D\\uDCCB I've analyzed the task and assembled this expert team. Let's begin the collaboration!\",\n            \"\\uD83C\\uDFAA Perfect! I've matched the right specialists to each part of this challenge.\",\n            \"⚡ Task breakdown complete. Our team is perfectly positioned for success.\"\n        ],\n        moderator_assignment: [\n            \"\\uD83C\\uDFAF @\".concat(roleId, \", you're up! Please begin your specialized work on this task.\"),\n            \"\\uD83D\\uDCCB @\".concat(roleId, \", this is right in your wheelhouse. Take it away!\"),\n            \"⚡ @\".concat(roleId, \", I need your expertise here. Please work your magic.\")\n        ],\n        specialist_acknowledgment: [\n            \"✅ Understood! I'm \".concat(roleId, \" and I'll handle this task with expertise. Starting work now...\"),\n            \"\\uD83C\\uDFAF Perfect! As the \".concat(roleId, \" specialist, I'm excited to tackle this challenge.\"),\n            \"⚡ Got it! \".concat(roleId, \" here - I'll deliver excellent results for the team.\")\n        ],\n        specialist_message: [\n            \"\\uD83C\\uDF89 Excellent! I've completed my part of the task. Here's what I've delivered:\",\n            \"✨ Perfect! My specialized work is complete. Take a look at the results:\",\n            \"\\uD83D\\uDE80 Mission accomplished! Here's my contribution to the team effort:\"\n        ],\n        handoff_message: [\n            \"✨ Excellent work, @\".concat(roleId, \"! Quality looks great. Now passing to the next specialist...\"),\n            \"\\uD83D\\uDC4F Outstanding execution, @\".concat(roleId, \"! Moving to the next phase...\"),\n            \"\\uD83C\\uDF89 Fantastic results, @\".concat(roleId, \"! The team collaboration continues...\")\n        ],\n        synthesis_started: [\n            \"\\uD83E\\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...\",\n            \"\\uD83C\\uDFA8 Amazing collaboration! Time to weave all these brilliant outputs together...\",\n            \"✨ Outstanding work team! Let me combine everything into the perfect final result...\"\n        ],\n        synthesis_complete: [\n            \"\\uD83C\\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!\",\n            \"\\uD83C\\uDFC6 Incredible teamwork! We've created something truly exceptional together.\",\n            \"\\uD83C\\uDF1F Perfect execution! This is what happens when AI specialists collaborate brilliantly.\"\n        ]\n    };\n    const options = messages[eventType] || [\n        \"\\uD83E\\uDD16 Processing...\"\n    ];\n    const selectedMessage = options[Math.floor(Math.random() * options.length)];\n    // Replace placeholders with context if available\n    if (context && typeof selectedMessage === \"string\") {\n        return selectedMessage.replace(/\\{(\\w+)\\}/g, (match, key)=>context[key] || match);\n    }\n    return selectedMessage;\n}\n// Generate specialist personality-based responses\nfunction generateSpecialistPersonalityMessage(roleId, messageType, context) {\n    const personalities = {\n        \"game-designer\": {\n            acknowledgment: [\n                \"\\uD83C\\uDFAE Absolutely! As a game designer, I'm excited to create something innovative and engaging.\",\n                \"\\uD83C\\uDFA8 Perfect! I love crafting unique gaming experiences. Let me dive into the creative process.\",\n                \"\\uD83C\\uDF1F Game design is my passion! I'll create something that players will absolutely love.\"\n            ],\n            clarification: [\n                \"\\uD83E\\uDD14 Quick question - should I focus more on gameplay mechanics or visual aesthetics?\",\n                \"\\uD83C\\uDFAF Just to clarify - are we targeting casual or hardcore gamers with this design?\",\n                \"\\uD83D\\uDCAD Before I proceed, what's the target platform? This will influence my design decisions.\"\n            ],\n            completion: [\n                \"\\uD83C\\uDF89 Game design complete! I've crafted an engaging experience with innovative mechanics.\",\n                \"✨ Design finished! This game concept balances fun, challenge, and player engagement perfectly.\",\n                \"\\uD83D\\uDE80 All done! I've created a game design that's both unique and highly playable.\"\n            ]\n        },\n        \"python-developer\": {\n            acknowledgment: [\n                \"\\uD83D\\uDC0D Got it! Python is my specialty - I'll write clean, efficient code for this project.\",\n                \"\\uD83D\\uDCBB Perfect! I love solving problems with Python. Let me code something amazing.\",\n                \"⚡ Python developer here! I'll implement this with best practices and optimal performance.\"\n            ],\n            clarification: [\n                \"\\uD83E\\uDD14 Should I prioritize performance optimization or code readability for this implementation?\",\n                \"\\uD83D\\uDCDA Quick question - any specific Python libraries or frameworks you'd prefer I use?\",\n                \"\\uD83D\\uDD27 Before coding, should I include error handling and logging, or keep it simple?\"\n            ],\n            completion: [\n                \"\\uD83C\\uDFAF Code complete! I've implemented a robust Python solution with clean architecture.\",\n                \"✅ Development finished! The code is well-structured, documented, and ready to run.\",\n                \"\\uD83D\\uDE80 All done! I've written efficient Python code that follows best practices.\"\n            ]\n        },\n        \"code-reviewer\": {\n            acknowledgment: [\n                \"\\uD83D\\uDD0D Excellent! I'll thoroughly review the code for quality, security, and best practices.\",\n                \"\\uD83D\\uDCCB Code review time! I'll ensure everything meets high standards and is production-ready.\",\n                \"\\uD83D\\uDEE1️ Perfect! I'll analyze the code for bugs, optimizations, and maintainability.\"\n            ],\n            clarification: [\n                \"\\uD83E\\uDD14 Should I focus on security vulnerabilities or performance optimizations in my review?\",\n                \"\\uD83D\\uDCCA Quick question - what's the priority: code quality, performance, or maintainability?\",\n                \"\\uD83D\\uDD27 Before reviewing, are there specific coding standards or guidelines I should follow?\"\n            ],\n            completion: [\n                \"✅ Code review complete! I've identified improvements and the code quality is excellent.\",\n                \"\\uD83C\\uDFAF Review finished! The code is solid with just a few minor optimization suggestions.\",\n                \"\\uD83C\\uDFC6 All done! This code meets high standards and is ready for production deployment.\"\n            ]\n        }\n    };\n    const defaultMessages = {\n        acknowledgment: [\n            \"✅ Understood! I'm the \".concat(roleId, \" specialist and I'll handle this with expertise.\"),\n            \"\\uD83C\\uDFAF Perfect! As \".concat(roleId, \", I'm excited to contribute to this project.\"),\n            \"⚡ Got it! \".concat(roleId, \" here - I'll deliver excellent results for the team.\")\n        ],\n        clarification: [\n            \"\\uD83E\\uDD14 Quick question before I proceed - could you clarify the specific requirements?\",\n            \"\\uD83D\\uDCAD Just to make sure I understand correctly - what's the main priority here?\",\n            \"\\uD83C\\uDFAF Before I start, should I focus on any particular aspect of this task?\"\n        ],\n        completion: [\n            \"\\uD83C\\uDF89 Task complete! I've delivered high-quality work as the \".concat(roleId, \" specialist.\"),\n            \"✨ All done! My specialized contribution is ready for the team.\",\n            \"\\uD83D\\uDE80 Mission accomplished! Here's my expert work as \".concat(roleId, \".\")\n        ]\n    };\n    const rolePersonality = personalities[roleId] || {};\n    const messages = rolePersonality[messageType] || defaultMessages[messageType] || [\n        \"\\uD83E\\uDD16 Processing...\"\n    ];\n    return messages[Math.floor(Math.random() * messages.length)];\n}\n// Generate moderator questions and clarifications\nfunction generateModeratorClarification(roleId, taskContext, clarificationType) {\n    const clarifications = {\n        assignment: [\n            \"@\".concat(roleId, \", I'm assigning this to you because of your expertise. Any questions before you start?\"),\n            \"@\".concat(roleId, \", this task is perfect for your skills. Do you need any clarification on the requirements?\"),\n            \"@\".concat(roleId, \", you're the ideal specialist for this. Ready to dive in, or need more context?\")\n        ],\n        handoff: [\n            \"@\".concat(roleId, \", excellent work! Before I pass this to the next specialist, any final thoughts?\"),\n            \"@\".concat(roleId, \", great job! Is there anything the next team member should know about your work?\"),\n            \"@\".concat(roleId, \", fantastic results! Any recommendations for the next phase?\")\n        ],\n        quality_check: [\n            \"@\".concat(roleId, \", I'm reviewing your work - it looks excellent! Can you walk me through your approach?\"),\n            \"@\".concat(roleId, \", impressive results! What was your key strategy for this task?\"),\n            \"@\".concat(roleId, \", outstanding work! Any challenges you overcame that the team should know about?\")\n        ]\n    };\n    const messages = clarifications[clarificationType] || clarifications.assignment;\n    return messages[Math.floor(Math.random() * messages.length)];\n}\n// Calculate optimal execution strategy (parallel vs sequential)\nfunction calculateExecutionStrategy(workflow) {\n    const totalSteps = workflow.steps.length;\n    const parallelGroups = workflow.parallelGroups.length;\n    if (parallelGroups === totalSteps) {\n        return {\n            strategy: \"sequential\",\n            estimatedSpeedup: 1.0,\n            riskLevel: \"low\"\n        };\n    }\n    if (parallelGroups === 1) {\n        return {\n            strategy: \"parallel\",\n            estimatedSpeedup: totalSteps * 0.7,\n            riskLevel: \"high\"\n        };\n    }\n    return {\n        strategy: \"hybrid\",\n        estimatedSpeedup: totalSteps / parallelGroups * 0.8,\n        riskLevel: \"medium\"\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/orchestrationUtils.ts\n"));

/***/ })

});