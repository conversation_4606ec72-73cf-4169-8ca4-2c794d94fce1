"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/ChatMessage.tsx":
/*!****************************************!*\
  !*** ./src/components/ChatMessage.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatMessage: function() { return /* binding */ ChatMessage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ChatMessage auto */ \n\n\nconst ChatMessage = (param)=>{\n    let { message } = param;\n    const getRoleColor = (roleId)=>{\n        if (!roleId) return \"from-blue-500 to-blue-600\"; // Moderator\n        // Generate consistent colors based on role name\n        const colors = [\n            \"from-green-500 to-green-600\",\n            \"from-purple-500 to-purple-600\",\n            \"from-orange-500 to-orange-600\",\n            \"from-pink-500 to-pink-600\",\n            \"from-indigo-500 to-indigo-600\",\n            \"from-teal-500 to-teal-600\",\n            \"from-red-500 to-red-600\",\n            \"from-yellow-500 to-yellow-600\"\n        ];\n        const hash = roleId.split(\"\").reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n        return colors[hash % colors.length];\n    };\n    const getRoleIcon = (sender, roleId)=>{\n        if (sender === \"moderator\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                lineNumber: 48,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 50,\n            columnNumber: 12\n        }, undefined);\n    };\n    const getMessageTypeIcon = (type)=>{\n        switch(type){\n            case \"assignment\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, undefined);\n            case \"completion\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, undefined);\n            case \"handoff\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, undefined);\n            case \"clarification\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-3 h-3 text-yellow-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    const formatTime = (timestamp)=>{\n        return timestamp.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const isFromModerator = message.sender === \"moderator\";\n    const roleColor = getRoleColor(message.roleId);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex \".concat(isFromModerator ? \"justify-start\" : \"justify-start\", \" mb-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[85%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r \".concat(roleColor, \" flex items-center justify-center text-white shadow-sm\"),\n                    children: getRoleIcon(message.sender, message.roleId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-semibold \".concat(isFromModerator ? \"text-blue-700\" : \"text-gray-700\"),\n                                    children: message.senderName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                getMessageTypeIcon(message.type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: getMessageTypeIcon(message.type)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block px-4 py-3 rounded-2xl shadow-sm \".concat(isFromModerator ? \"bg-blue-50 border border-blue-100\" : \"bg-gray-50 border border-gray-100\", \" \").concat(message.type === \"completion\" ? \"border-green-200 bg-green-50\" : message.type === \"assignment\" ? \"border-blue-200 bg-blue-50\" : message.type === \"handoff\" ? \"border-purple-200 bg-purple-50\" : message.type === \"clarification\" ? \"border-yellow-200 bg-yellow-50\" : \"\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm leading-relaxed \".concat(isFromModerator ? \"text-blue-900\" : \"text-gray-800\", \" \").concat(message.type === \"completion\" ? \"text-green-900\" : message.type === \"assignment\" ? \"text-blue-900\" : message.type === \"handoff\" ? \"text-purple-900\" : message.type === \"clarification\" ? \"text-yellow-900\" : \"\"),\n                                children: message.content.split(\"\\n\").map((line, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                        children: [\n                                            line,\n                                            index < message.content.split(\"\\n\").length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 70\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, undefined),\n                        message.type !== \"message\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(message.type === \"assignment\" ? \"bg-blue-100 text-blue-800\" : message.type === \"completion\" ? \"bg-green-100 text-green-800\" : message.type === \"handoff\" ? \"bg-purple-100 text-purple-800\" : message.type === \"clarification\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-gray-100 text-gray-800\"),\n                                children: [\n                                    message.type === \"assignment\" && \"\\uD83D\\uDCCB Task Assignment\",\n                                    message.type === \"completion\" && \"✅ Task Complete\",\n                                    message.type === \"handoff\" && \"\\uD83D\\uDD04 Handoff\",\n                                    message.type === \"clarification\" && \"❓ Question\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatMessage.tsx\n"));

/***/ })

});