"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationCanvas.tsx":
/*!************************************************!*\
  !*** ./src/components/OrchestrationCanvas.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationCanvas: function() { return /* binding */ OrchestrationCanvas; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OrchestrationChatroom */ \"(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationCanvas = (param)=>{\n    let { executionId, onComplete, onError, onCanvasStateChange } = param;\n    _s();\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orchestrationComplete, setOrchestrationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { events, isConnected, error } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Handle orchestration completion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const synthesisCompleteEvent = events.find((event)=>event.type === \"synthesis_complete\");\n        if (synthesisCompleteEvent && !orchestrationComplete) {\n            var _synthesisCompleteEvent_data;\n            setOrchestrationComplete(true);\n            const result = ((_synthesisCompleteEvent_data = synthesisCompleteEvent.data) === null || _synthesisCompleteEvent_data === void 0 ? void 0 : _synthesisCompleteEvent_data.result) || \"Orchestration completed successfully\";\n            setFinalResult(result);\n            // Notify parent component\n            if (onComplete) {\n                onComplete(result);\n            }\n        }\n    }, [\n        events,\n        orchestrationComplete,\n        onComplete\n    ]);\n    // Handle errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (error && onError) {\n            onError(error);\n        }\n    }, [\n        error,\n        onError\n    ]);\n    const handleMinimize = ()=>{\n        setIsMinimized(true);\n        setIsCanvasOpen(false);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(false, true);\n    };\n    const handleMaximize = ()=>{\n        setIsMinimized(false);\n        setIsCanvasOpen(true);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(true, false);\n    };\n    // Notify parent of initial canvas state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(isCanvasOpen, isMinimized);\n    }, [\n        isCanvasOpen,\n        isMinimized,\n        onCanvasStateChange\n    ]);\n    // Minimized card state - now returns null, will be rendered inline in chat\n    if (isMinimized) {\n        return null;\n    }\n    // Canvas is closed\n    if (!isCanvasOpen) {\n        return null;\n    }\n    // Debug log when rendering\n    console.log(\"\\uD83C\\uDFAD [DEBUG] OrchestrationCanvas is rendering!\", {\n        isCanvasOpen,\n        isMinimized,\n        executionId,\n        shouldBeVisible: isCanvasOpen && !isMinimized,\n        transformClass: isCanvasOpen ? \"translate-x-0\" : \"translate-x-full\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-0 right-0 h-full w-1/2 bg-white shadow-2xl z-[9999] transform transition-transform duration-300 ease-out border-l border-gray-200 \".concat(isCanvasOpen ? \"translate-x-0\" : \"translate-x-full\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-4 left-4 bg-green-500 text-white p-2 rounded z-[10000] text-xs font-bold\",\n                    children: [\n                        \"SPLIT-SCREEN CANVAS - isOpen: \",\n                        isCanvasOpen.toString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"AI Team Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: orchestrationComplete ? \"Orchestration Complete\" : \"Multi-Role Orchestration in Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleMinimize,\n                                className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200\",\n                                \"aria-label\": \"Minimize canvas\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 h-full overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__.OrchestrationChatroom, {\n                        executionId: executionId,\n                        events: events,\n                        isConnected: isConnected,\n                        error: error,\n                        isComplete: orchestrationComplete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(OrchestrationCanvas, \"RDHLp+HVHmkFq5YA06E2O93JbzM=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = OrchestrationCanvas;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\n"));

/***/ })

});