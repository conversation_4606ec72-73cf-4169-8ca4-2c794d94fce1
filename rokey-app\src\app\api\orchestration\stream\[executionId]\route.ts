// Real-time orchestration streaming endpoint for live AI team collaboration

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import {
  OrchestrationEvent,
  addActiveStream,
  removeActiveStream,
  setStreamHeartbeat,
  getActiveStream,
  getActiveStreamIds,
  formatSSEEvent,
  broadcastOrchestrationEvent,
  cleanupOldStreams
} from '@/lib/orchestration-stream-manager';

export async function GET(
  request: NextRequest,
  { params }: { params: { executionId: string } }
) {
  const { executionId } = params;
  
  if (!executionId) {
    return NextResponse.json(
      { error: 'Execution ID is required' },
      { status: 400 }
    );
  }

  const supabase = createSupabaseServerClientOnRequest();
  
  try {
    // Verify execution exists and user has access
    const { data: execution, error } = await supabase
      .from('orchestration_executions')
      .select('*')
      .eq('id', executionId)
      .single();

    if (error || !execution) {
      return NextResponse.json(
        { error: 'Orchestration execution not found' },
        { status: 404 }
      );
    }

    // Create Server-Sent Events stream
    const encoder = new TextEncoder();
    let streamController: ReadableStreamDefaultController;

    const stream = new ReadableStream({
      start(controller) {
        streamController = controller;
        
        // Store stream reference for broadcasting events using shared manager
        addActiveStream(executionId, {
          controller,
          lastEventId: '',
          startTime: Date.now()
        });

        // Send initial connection event
        const connectionEvent = {
          id: crypto.randomUUID(),
          execution_id: executionId,
          type: 'stream_connected',
          timestamp: new Date().toISOString(),
          data: {
            message: '🎬 Connected to AI team orchestration stream',
            execution: {
              id: execution.id,
              status: execution.status,
              total_steps: execution.total_steps,
              created_at: execution.created_at
            }
          }
        };

        console.log(`🎭 [STREAM DEBUG] Client connected to orchestration stream for execution: ${executionId}`);
        console.log(`🎭 [STREAM DEBUG] Sending initial connection event:`, connectionEvent);
        console.log(`🎭 [STREAM DEBUG] Active streams after connection:`, getActiveStreamIds());

        const eventData = formatSSEEvent(connectionEvent);
        controller.enqueue(encoder.encode(eventData));

        // Send historical events if execution is in progress or completed
        if (execution.status !== 'pending') {
          sendHistoricalEvents(executionId, controller, encoder);
        }

        console.log(`[Orchestration Stream] Client connected to execution ${executionId}`);

        // Send a heartbeat every 10 seconds to keep connection alive (more frequent for debugging)
        const heartbeatInterval = setInterval(() => {
          try {
            if (getActiveStream(executionId)) {
              const heartbeatEvent = {
                id: crypto.randomUUID(),
                execution_id: executionId,
                type: 'heartbeat',
                timestamp: new Date().toISOString(),
                data: { message: 'Connection alive' }
              };
              const heartbeatData = formatSSEEvent(heartbeatEvent);
              controller.enqueue(encoder.encode(heartbeatData));
              console.log(`🎭 [STREAM DEBUG] Sent heartbeat for execution ${executionId} - Active streams: ${getActiveStreamIds()}`);
            } else {
              console.log(`🎭 [STREAM DEBUG] Stream ${executionId} no longer active, stopping heartbeat`);
              clearInterval(heartbeatInterval);
            }
          } catch (error) {
            console.error(`🎭 [STREAM DEBUG] Heartbeat error for ${executionId}:`, error);
            clearInterval(heartbeatInterval);
            removeActiveStream(executionId);
          }
        }, 10000);

        // Store the interval for cleanup using shared manager
        setStreamHeartbeat(executionId, heartbeatInterval);
      },

      cancel() {
        // Clean up stream reference using shared manager
        removeActiveStream(executionId);
        console.log(`🎭 [STREAM DEBUG] Client disconnected from execution ${executionId}`);
        console.log(`🎭 [STREAM DEBUG] Active streams after disconnect:`, getActiveStreamIds());
      }
    });

    // Return SSE response
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    });

  } catch (error) {
    console.error(`[Orchestration Stream] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Send historical events for ongoing or completed executions
async function sendHistoricalEvents(
  executionId: string,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder
) {
  const supabase = createSupabaseServerClientOnRequest();
  
  try {
    // Get orchestration steps with their current status
    const { data: steps, error } = await supabase
      .from('orchestration_steps')
      .select('*')
      .eq('execution_id', executionId)
      .order('step_number', { ascending: true });

    if (error) {
      console.error(`[Orchestration Stream] Error fetching steps: ${error}`);
      return;
    }

    // Send events for each step based on its status
    for (const step of steps || []) {
      const events = generateHistoricalEventsForStep(step, executionId);
      
      for (const event of events) {
        const eventData = formatSSEEvent(event);
        controller.enqueue(encoder.encode(eventData));
        
        // Small delay between events for better UX
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

  } catch (error) {
    console.error(`[Orchestration Stream] Error sending historical events: ${error}`);
  }
}

// Generate historical events based on step status
function generateHistoricalEventsForStep(step: any, executionId: string): OrchestrationEvent[] {
  const events: OrchestrationEvent[] = [];
  const baseEvent = {
    execution_id: executionId,
    step_number: step.step_number,
    role_id: step.role_id,
    model_name: step.model_name
  };

  // Step assigned event
  events.push({
    id: crypto.randomUUID(),
    ...baseEvent,
    type: 'step_assigned',
    timestamp: step.created_at,
    data: {
      commentary: `📋 ${step.role_id} specialist assigned to step ${step.step_number}`,
      step: {
        number: step.step_number,
        role: step.role_id,
        model: step.model_name,
        prompt: step.prompt.substring(0, 100) + '...'
      }
    }
  });

  // Step started event (if started)
  if (step.started_at) {
    events.push({
      id: crypto.randomUUID(),
      ...baseEvent,
      type: 'step_started',
      timestamp: step.started_at,
      data: {
        commentary: `🚀 ${step.role_id} is now working on this challenge...`,
        estimatedDuration: step.duration_ms || 45000
      }
    });
  }

  // Step progress events (if in progress)
  if (step.status === 'in_progress') {
    events.push({
      id: crypto.randomUUID(),
      ...baseEvent,
      type: 'step_progress',
      timestamp: new Date().toISOString(),
      data: {
        commentary: `⚡ ${step.role_id} is making excellent progress...`,
        progress: 0.6,
        partialOutput: step.response ? step.response.substring(0, 200) + '...' : null
      }
    });
  }

  // Step completed event (if completed)
  if (step.status === 'completed' && step.completed_at) {
    events.push({
      id: crypto.randomUUID(),
      ...baseEvent,
      type: 'step_completed',
      timestamp: step.completed_at,
      data: {
        commentary: `✅ Outstanding work from ${step.role_id}! Moving to next phase.`,
        output: step.response,
        duration: step.duration_ms,
        tokens: {
          input: step.tokens_in,
          output: step.tokens_out
        },
        cost: step.cost,
        quality: 0.9 // Could be calculated based on validation
      }
    });
  }

  // Step failed event (if failed)
  if (step.status === 'failed') {
    events.push({
      id: crypto.randomUUID(),
      ...baseEvent,
      type: 'step_failed',
      timestamp: step.completed_at || new Date().toISOString(),
      data: {
        commentary: `❌ ${step.role_id} encountered an issue. Analyzing options...`,
        error: step.error_message,
        retryPlan: 'Attempting automatic recovery'
      }
    });
  }

  return events;
}

// These functions are now provided by the shared orchestration-stream-manager
