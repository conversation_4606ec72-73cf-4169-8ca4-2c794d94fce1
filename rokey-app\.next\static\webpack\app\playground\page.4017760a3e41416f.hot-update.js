"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/ChatMessage.tsx":
/*!****************************************!*\
  !*** ./src/components/ChatMessage.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatMessage: function() { return /* binding */ ChatMessage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ChatMessage auto */ \n\n\nconst ChatMessage = (param)=>{\n    let { message } = param;\n    const getRoleColor = (roleId)=>{\n        if (!roleId) return \"from-blue-500 to-blue-600\"; // Moderator\n        // Generate consistent colors based on role name\n        const colors = [\n            \"from-green-500 to-green-600\",\n            \"from-purple-500 to-purple-600\",\n            \"from-orange-500 to-orange-600\",\n            \"from-pink-500 to-pink-600\",\n            \"from-indigo-500 to-indigo-600\",\n            \"from-teal-500 to-teal-600\",\n            \"from-red-500 to-red-600\",\n            \"from-yellow-500 to-yellow-600\"\n        ];\n        const hash = roleId.split(\"\").reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n        return colors[hash % colors.length];\n    };\n    const getRoleIcon = (sender, roleId)=>{\n        if (sender === \"moderator\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                lineNumber: 48,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 50,\n            columnNumber: 12\n        }, undefined);\n    };\n    const getMessageTypeIcon = (type)=>{\n        switch(type){\n            case \"assignment\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, undefined);\n            case \"completion\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, undefined);\n            case \"handoff\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    const formatTime = (timestamp)=>{\n        return timestamp.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const isFromModerator = message.sender === \"moderator\";\n    const roleColor = getRoleColor(message.roleId);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex \".concat(isFromModerator ? \"justify-start\" : \"justify-start\", \" mb-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[85%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r \".concat(roleColor, \" flex items-center justify-center text-white shadow-sm\"),\n                    children: getRoleIcon(message.sender, message.roleId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-semibold \".concat(isFromModerator ? \"text-blue-700\" : \"text-gray-700\"),\n                                    children: message.senderName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined),\n                                getMessageTypeIcon(message.type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: getMessageTypeIcon(message.type)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block px-4 py-3 rounded-2xl shadow-sm \".concat(isFromModerator ? \"bg-blue-50 border border-blue-100\" : \"bg-gray-50 border border-gray-100\", \" \").concat(message.type === \"completion\" ? \"border-green-200 bg-green-50\" : message.type === \"assignment\" ? \"border-blue-200 bg-blue-50\" : message.type === \"handoff\" ? \"border-purple-200 bg-purple-50\" : message.type === \"clarification\" ? \"border-yellow-200 bg-yellow-50\" : \"\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm leading-relaxed \".concat(isFromModerator ? \"text-blue-900\" : \"text-gray-800\", \" \").concat(message.type === \"completion\" ? \"text-green-900\" : message.type === \"assignment\" ? \"text-blue-900\" : message.type === \"handoff\" ? \"text-purple-900\" : \"\"),\n                                children: message.content.split(\"\\n\").map((line, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                        children: [\n                                            line,\n                                            index < message.content.split(\"\\n\").length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 70\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined),\n                        message.type !== \"message\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(message.type === \"assignment\" ? \"bg-blue-100 text-blue-800\" : message.type === \"completion\" ? \"bg-green-100 text-green-800\" : message.type === \"handoff\" ? \"bg-purple-100 text-purple-800\" : message.type === \"clarification\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-gray-100 text-gray-800\"),\n                                children: [\n                                    message.type === \"assignment\" && \"\\uD83D\\uDCCB Task Assignment\",\n                                    message.type === \"completion\" && \"✅ Task Complete\",\n                                    message.type === \"handoff\" && \"\\uD83D\\uDD04 Handoff\",\n                                    message.type === \"clarification\" && \"❓ Clarification\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatMessage.tsx\n"));

/***/ })

});