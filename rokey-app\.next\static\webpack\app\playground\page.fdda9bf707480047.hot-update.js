"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationCanvas.tsx":
/*!************************************************!*\
  !*** ./src/components/OrchestrationCanvas.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationCanvas: function() { return /* binding */ OrchestrationCanvas; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OrchestrationChatroom */ \"(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationCanvas = (param)=>{\n    let { executionId, onComplete, onError, onCanvasStateChange } = param;\n    _s();\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orchestrationComplete, setOrchestrationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { events, isConnected, error } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Handle orchestration completion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const synthesisCompleteEvent = events.find((event)=>event.type === \"synthesis_complete\");\n        if (synthesisCompleteEvent && !orchestrationComplete) {\n            var _synthesisCompleteEvent_data;\n            setOrchestrationComplete(true);\n            const result = ((_synthesisCompleteEvent_data = synthesisCompleteEvent.data) === null || _synthesisCompleteEvent_data === void 0 ? void 0 : _synthesisCompleteEvent_data.result) || \"Orchestration completed successfully\";\n            setFinalResult(result);\n            // Notify parent component\n            if (onComplete) {\n                onComplete(result);\n            }\n        }\n    }, [\n        events,\n        orchestrationComplete,\n        onComplete\n    ]);\n    // Handle errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (error && onError) {\n            onError(error);\n        }\n    }, [\n        error,\n        onError\n    ]);\n    const handleMinimize = ()=>{\n        setIsMinimized(true);\n        setIsCanvasOpen(false);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(false, true);\n    };\n    const handleMaximize = ()=>{\n        setIsMinimized(false);\n        setIsCanvasOpen(true);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(true, false);\n    };\n    const handleClose = ()=>{\n        setIsCanvasOpen(false);\n        setIsMinimized(false);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(false, false);\n    };\n    // Notify parent of initial canvas state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(isCanvasOpen, isMinimized);\n    }, [\n        isCanvasOpen,\n        isMinimized,\n        onCanvasStateChange\n    ]);\n    // Minimized card state - now returns null, will be rendered inline in chat\n    if (isMinimized) {\n        return null;\n    }\n    // Canvas is closed\n    if (!isCanvasOpen) {\n        return null;\n    }\n    // Debug log when rendering\n    console.log(\"\\uD83C\\uDFAD [DEBUG] OrchestrationCanvas is rendering!\", {\n        isCanvasOpen,\n        isMinimized,\n        executionId,\n        shouldBeVisible: isCanvasOpen && !isMinimized,\n        transformClass: isCanvasOpen ? \"translate-x-0\" : \"translate-x-full\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-0 right-0 h-full w-1/2 bg-white shadow-2xl z-[9999] transform transition-transform duration-300 ease-out border-l border-gray-200 \".concat(isCanvasOpen ? \"translate-x-0\" : \"translate-x-full\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-4 left-4 bg-green-500 text-white p-2 rounded z-[10000] text-xs font-bold\",\n                    children: [\n                        \"SPLIT-SCREEN CANVAS - isOpen: \",\n                        isCanvasOpen.toString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"AI Team Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: orchestrationComplete ? \"Orchestration Complete\" : \"Multi-Role Orchestration in Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleMinimize,\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200\",\n                                    \"aria-label\": \"Minimize canvas\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClose,\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200\",\n                                    \"aria-label\": \"Close canvas\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(XMarkIcon, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 h-full overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__.OrchestrationChatroom, {\n                        executionId: executionId,\n                        events: events,\n                        isConnected: isConnected,\n                        error: error,\n                        isComplete: orchestrationComplete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(OrchestrationCanvas, \"RDHLp+HVHmkFq5YA06E2O93JbzM=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = OrchestrationCanvas;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\n"));

/***/ })

});