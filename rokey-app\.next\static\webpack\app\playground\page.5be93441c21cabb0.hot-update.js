"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/app/playground/page.tsx":
/*!*************************************!*\
  !*** ./src/app/playground/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlaygroundPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperClipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilSquareIcon.js\");\n/* harmony import */ var _components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LazyMarkdownRenderer */ \"(app-pages-browser)/./src/components/LazyMarkdownRenderer.tsx\");\n/* harmony import */ var _components_CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* harmony import */ var _components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/RetryDropdown */ \"(app-pages-browser)/./src/components/RetryDropdown.tsx\");\n/* harmony import */ var _components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DynamicStatusIndicator */ \"(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\");\n/* harmony import */ var _components_OrchestrationCanvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/OrchestrationCanvas */ \"(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useMessageStatus */ \"(app-pages-browser)/./src/hooks/useMessageStatus.ts\");\n/* harmony import */ var _utils_performanceLogs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/performanceLogs */ \"(app-pages-browser)/./src/utils/performanceLogs.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Temporarily comment out to fix import issue\n// import { ChatHistorySkeleton, EnhancedChatHistorySkeleton, MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';\n\n\n// import VirtualChatHistory from '@/components/VirtualChatHistory';\n// Import performance logging utilities for browser console access\n\n// Memoized chat history item component to prevent unnecessary re-renders\nconst ChatHistoryItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { chat, currentConversation, onLoadChat, onDeleteChat } = param;\n    const isActive = (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === chat.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 \".concat(isActive ? \"bg-orange-50 border border-orange-200\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onLoadChat(chat),\n                className: \"w-full text-left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 truncate mb-1\",\n                                children: chat.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined),\n                            chat.last_message_preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 line-clamp-2 mb-2\",\n                                children: chat.last_message_preview\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            chat.message_count,\n                                            \" messages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: new Date(chat.updated_at).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    onDeleteChat(chat.id);\n                },\n                className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200\",\n                title: \"Delete conversation\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\n_c = ChatHistoryItem;\nfunction PlaygroundPage() {\n    _s();\n    const { isCollapsed, isHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar)();\n    // Calculate actual sidebar width (collapsed but can expand on hover)\n    const sidebarWidth = !isCollapsed || isHovered ? \"256px\" : \"64px\";\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialPageLoad, setInitialPageLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Prefetch API keys when config is selected for faster retry dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId) {\n            // Prefetch keys in background for retry dropdown\n            fetch(\"/api/keys?custom_config_id=\".concat(selectedConfigId)).then((response)=>response.json()).catch((error)=>console.log(\"Background key prefetch failed:\", error));\n        }\n    }, [\n        selectedConfigId\n    ]);\n    const [messageInput, setMessageInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useStreaming, setUseStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollToBottom, setShowScrollToBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for multiple image handling (up to 10 images)\n    const [imageFiles, setImageFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [imagePreviews, setImagePreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // History sidebar state\n    const [isHistoryCollapsed, setIsHistoryCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Edit message state\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingText, setEditingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoadingMessages, setIsLoadingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Orchestration state\n    const [orchestrationExecutionId, setOrchestrationExecutionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrchestration, setShowOrchestration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Canvas state for split-screen layout\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCanvasMinimized, setIsCanvasMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle canvas state changes\n    const handleCanvasStateChange = (canvasOpen, canvasMinimized)=>{\n        setIsCanvasOpen(canvasOpen);\n        setIsCanvasMinimized(canvasMinimized);\n        // Auto-minimize history sidebar when canvas opens\n        if (canvasOpen && !canvasMinimized) {\n            setIsHistoryCollapsed(true);\n        }\n    };\n    // Debug logs for orchestration state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDFAD [DEBUG] Orchestration state changed:\", {\n            showOrchestration,\n            orchestrationExecutionId,\n            isCanvasOpen,\n            isCanvasMinimized,\n            shouldRenderCanvas: showOrchestration && orchestrationExecutionId,\n            timestamp: new Date().toISOString()\n        });\n        if (showOrchestration && orchestrationExecutionId) {\n            console.log(\"\\uD83C\\uDFAD [DEBUG] Canvas should be visible now!\");\n        }\n    }, [\n        showOrchestration,\n        orchestrationExecutionId,\n        isCanvasOpen,\n        isCanvasMinimized\n    ]);\n    // Enhanced status tracking\n    const messageStatus = (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus)({\n        enableAutoProgression: true,\n        onStageChange: (stage, timestamp)=>{\n            console.log(\"\\uD83C\\uDFAF Status: \".concat(stage, \" at \").concat(timestamp));\n        }\n    });\n    // Orchestration status tracking\n    const [orchestrationStatus, setOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Function to update orchestration status based on streaming content\n    const updateOrchestrationStatus = (deltaContent, messageStatusObj)=>{\n        let newStatus = \"\";\n        if (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\")) {\n            newStatus = \"Multi-Role AI Orchestration Started\";\n        } else if (deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\")) {\n            newStatus = \"Planning specialist assignments\";\n        } else if (deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\")) {\n            newStatus = \"Moderator coordinating specialists\";\n        } else if (deltaContent.includes(\"Specialist:\") && deltaContent.includes(\"Working...\")) {\n            // Extract specialist name\n            const specialistMatch = deltaContent.match(/(\\w+)\\s+Specialist:/);\n            if (specialistMatch) {\n                newStatus = \"\".concat(specialistMatch[1], \" Specialist working\");\n            } else {\n                newStatus = \"Specialist working on your request\";\n            }\n        } else if (deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\")) {\n            newStatus = \"Synthesizing specialist responses\";\n        } else if (deltaContent.includes(\"Analyzing and processing\")) {\n            newStatus = \"Analyzing and processing with specialized expertise\";\n        }\n        if (newStatus && newStatus !== orchestrationStatus) {\n            console.log(\"\\uD83C\\uDFAD Orchestration status update:\", newStatus);\n            setOrchestrationStatus(newStatus);\n            messageStatusObj.updateOrchestrationStatus(newStatus);\n        }\n    };\n    // Auto-continuation function for seamless multi-part responses\n    const handleAutoContinuation = async ()=>{\n        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Starting automatic continuation...\");\n        if (!selectedConfigId || !currentConversation) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Missing config or conversation\");\n            return;\n        }\n        setIsLoading(true);\n        setOrchestrationStatus(\"Continuing synthesis automatically...\");\n        messageStatus.startProcessing();\n        try {\n            // Create a continuation message\n            const continuationMessage = {\n                id: Date.now().toString() + \"-continue\",\n                role: \"user\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"continue\"\n                    }\n                ]\n            };\n            // Add the continuation message to the UI\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    continuationMessage\n                ]);\n            // Save continuation message to database\n            await saveMessageToDatabase(currentConversation.id, continuationMessage);\n            // Prepare payload for continuation\n            const continuationPayload = {\n                custom_api_config_id: selectedConfigId,\n                messages: [\n                    ...messages.map((m)=>({\n                            role: m.role,\n                            content: m.content.length === 1 && m.content[0].type === \"text\" ? m.content[0].text : m.content\n                        })),\n                    {\n                        role: \"user\",\n                        content: \"continue\"\n                    }\n                ],\n                stream: useStreaming\n            };\n            // Make the continuation request\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(continuationPayload),\n                cache: \"no-store\"\n            });\n            // Check for synthesis completion response\n            if (response.ok) {\n                // Check if this is a synthesis completion response\n                const responseText = await response.text();\n                let responseData;\n                try {\n                    responseData = JSON.parse(responseText);\n                } catch (e) {\n                    // If it's not JSON, treat as regular response\n                    responseData = null;\n                }\n                // Handle synthesis completion\n                if ((responseData === null || responseData === void 0 ? void 0 : responseData.error) === \"synthesis_complete\") {\n                    console.log('\\uD83C\\uDF89 [AUTO-CONTINUE] Synthesis is complete! Treating \"continue\" as new conversation.');\n                    // Remove the continuation message we just added\n                    setMessages((prevMessages)=>prevMessages.slice(0, -1));\n                    // Clear the loading state\n                    setIsLoading(false);\n                    setOrchestrationStatus(\"\");\n                    messageStatus.markComplete();\n                    // Process the \"continue\" as a new message by calling the normal send flow\n                    // But first we need to set the input back to \"continue\"\n                    setMessageInput(\"continue\");\n                    // Call the normal send message flow which will handle it as a new conversation\n                    setTimeout(()=>{\n                        handleSendMessage();\n                    }, 100);\n                    return;\n                }\n                // If not synthesis completion, recreate the response for normal processing\n                const recreatedResponse = new Response(responseText, {\n                    status: response.status,\n                    statusText: response.statusText,\n                    headers: response.headers\n                });\n                // Handle the continuation response\n                if (useStreaming && recreatedResponse.body) {\n                    const reader = recreatedResponse.body.getReader();\n                    const decoder = new TextDecoder();\n                    let assistantMessageId = Date.now().toString() + \"-assistant-continue\";\n                    let currentAssistantMessage = {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: [\n                            {\n                                type: \"text\",\n                                text: \"\"\n                            }\n                        ]\n                    };\n                    setMessages((prevMessages)=>[\n                            ...prevMessages,\n                            currentAssistantMessage\n                        ]);\n                    let accumulatedText = \"\";\n                    let isOrchestrationDetected = false;\n                    let streamingStatusTimeout = null;\n                    // Check response headers to determine if this is chunked synthesis continuation\n                    const synthesisProgress = recreatedResponse.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = recreatedResponse.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    if (isChunkedSynthesis) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation\");\n                        messageStatus.markStreaming();\n                        setOrchestrationStatus(\"\");\n                    } else {\n                        // Start with continuation status, but allow orchestration detection to override\n                        messageStatus.markOrchestrationStarted();\n                        setOrchestrationStatus(\"Continuing synthesis...\");\n                        // Set up delayed streaming status, but allow orchestration detection to override\n                        streamingStatusTimeout = setTimeout(()=>{\n                            if (!isOrchestrationDetected) {\n                                console.log(\"\\uD83C\\uDFAF [AUTO-CONTINUE] No orchestration detected - switching to typing status\");\n                                messageStatus.markStreaming();\n                                setOrchestrationStatus(\"\");\n                            }\n                        }, 800);\n                    }\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const jsonData = line.substring(6);\n                                if (jsonData.trim() === \"[DONE]\") break;\n                                try {\n                                    var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                    const parsedChunk = JSON.parse(jsonData);\n                                    if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                        const deltaContent = parsedChunk.choices[0].delta.content;\n                                        accumulatedText += deltaContent;\n                                        // Only check for orchestration if this is NOT a chunked synthesis continuation\n                                        if (!isChunkedSynthesis && !isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                            console.log(\"\\uD83C\\uDFAD [AUTO-CONTINUE] Detected NEW orchestration - this should be direct continuation instead\");\n                                            isOrchestrationDetected = true;\n                                            // Cancel the delayed streaming status\n                                            if (streamingStatusTimeout) {\n                                                clearTimeout(streamingStatusTimeout);\n                                                streamingStatusTimeout = null;\n                                            }\n                                            // Update orchestration status for new orchestration\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else if (!isChunkedSynthesis && isOrchestrationDetected) {\n                                            // Continue updating orchestration status if already detected\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else {\n                                        // This is direct continuation content (chunked synthesis or regular continuation)\n                                        // Keep the current status without changing it\n                                        }\n                                        const textContent = currentAssistantMessage.content[0];\n                                        textContent.text = accumulatedText;\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: [\n                                                        textContent\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Auto-continuation: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                                }\n                            }\n                        }\n                    }\n                    // Clean up timeout if still pending\n                    if (streamingStatusTimeout) {\n                        clearTimeout(streamingStatusTimeout);\n                    }\n                    // Save the continuation response\n                    if (accumulatedText) {\n                        const finalContinuationMessage = {\n                            ...currentAssistantMessage,\n                            content: [\n                                {\n                                    type: \"text\",\n                                    text: accumulatedText\n                                }\n                            ]\n                        };\n                        // Check if we need auto-continuation for chunked synthesis\n                        const needsAutoContinuation = isChunkedSynthesis && synthesisComplete !== \"true\" && accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\");\n                        if (needsAutoContinuation) {\n                            console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation, starting next chunk...\");\n                            // Save current message first\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                            // Start auto-continuation after a brief delay\n                            setTimeout(()=>{\n                                handleAutoContinuation();\n                            }, 1000);\n                        } else {\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                        }\n                    }\n                }\n            } else {\n                // Handle non-ok response\n                throw new Error(\"Auto-continuation failed: \".concat(response.status));\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Error:\", error);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-continue\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"Auto-continuation failed: \".concat(error instanceof Error ? error.message : \"Unknown error\")\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n            setOrchestrationStatus(\"\");\n            messageStatus.markComplete();\n        }\n    };\n    // Enhanced chat history with optimized caching\n    const { chatHistory, isLoading: isLoadingHistory, isStale: isChatHistoryStale, error: chatHistoryError, refetch: refetchChatHistory, prefetch: prefetchChatHistory, invalidateCache: invalidateChatHistoryCache } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory)({\n        configId: selectedConfigId,\n        enablePrefetch: true,\n        cacheTimeout: 300000,\n        staleTimeout: 30000 // 30 seconds - show stale data while fetching fresh\n    });\n    // Chat history prefetching hook\n    const { prefetchChatHistory: prefetchForNavigation } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    // Conversation starters\n    const conversationStarters = [\n        {\n            id: \"write-copy\",\n            title: \"Write copy\",\n            description: \"Create compelling marketing content\",\n            icon: \"✍️\",\n            color: \"bg-amber-100 text-amber-700\",\n            prompt: \"Help me write compelling copy for my product landing page\"\n        },\n        {\n            id: \"image-generation\",\n            title: \"Image generation\",\n            description: \"Create visual content descriptions\",\n            icon: \"\\uD83C\\uDFA8\",\n            color: \"bg-blue-100 text-blue-700\",\n            prompt: \"Help me create detailed prompts for AI image generation\"\n        },\n        {\n            id: \"create-avatar\",\n            title: \"Create avatar\",\n            description: \"Design character personas\",\n            icon: \"\\uD83D\\uDC64\",\n            color: \"bg-green-100 text-green-700\",\n            prompt: \"Help me create a detailed character avatar for my story\"\n        },\n        {\n            id: \"write-code\",\n            title: \"Write code\",\n            description: \"Generate and debug code\",\n            icon: \"\\uD83D\\uDCBB\",\n            color: \"bg-purple-100 text-purple-700\",\n            prompt: \"Help me write clean, efficient code for my project\"\n        }\n    ];\n    // Fetch Custom API Configs for the dropdown with progressive loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchConfigs = async ()=>{\n            try {\n                // Progressive loading: render UI first, then load configs\n                if (initialPageLoad) {\n                    await new Promise((resolve)=>setTimeout(resolve, 50));\n                }\n                const response = await fetch(\"/api/custom-configs\");\n                if (!response.ok) {\n                    const errData = await response.json();\n                    throw new Error(errData.error || \"Failed to fetch configurations\");\n                }\n                const data = await response.json();\n                setCustomConfigs(data);\n                if (data.length > 0) {\n                    setSelectedConfigId(data[0].id);\n                }\n                setInitialPageLoad(false);\n            } catch (err) {\n                setError(\"Failed to load configurations: \".concat(err.message));\n                setCustomConfigs([]);\n                setInitialPageLoad(false);\n            }\n        };\n        // Call immediately to ensure configs load properly\n        fetchConfigs();\n    }, [\n        initialPageLoad\n    ]);\n    // Helper function to convert File to base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    const handleImageChange = async (event)=>{\n        const files = Array.from(event.target.files || []);\n        if (files.length === 0) return;\n        // Limit to 10 images total\n        const currentCount = imageFiles.length;\n        const availableSlots = 10 - currentCount;\n        const filesToAdd = files.slice(0, availableSlots);\n        if (filesToAdd.length < files.length) {\n            setError(\"You can only upload up to 10 images. \".concat(files.length - filesToAdd.length, \" images were not added.\"));\n        }\n        try {\n            const newPreviews = [];\n            for (const file of filesToAdd){\n                const previewUrl = await fileToBase64(file);\n                newPreviews.push(previewUrl);\n            }\n            setImageFiles((prev)=>[\n                    ...prev,\n                    ...filesToAdd\n                ]);\n            setImagePreviews((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error processing images:\", error);\n            setError(\"Failed to process one or more images. Please try again.\");\n        }\n        // Reset file input\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleRemoveImage = (index)=>{\n        if (index !== undefined) {\n            // Remove specific image\n            setImageFiles((prev)=>prev.filter((_, i)=>i !== index));\n            setImagePreviews((prev)=>prev.filter((_, i)=>i !== index));\n        } else {\n            // Remove all images\n            setImageFiles([]);\n            setImagePreviews([]);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\"; // Reset file input\n        }\n    };\n    // Scroll management functions\n    const scrollToBottom = function() {\n        let smooth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (messagesContainerRef.current) {\n            messagesContainerRef.current.scrollTo({\n                top: messagesContainerRef.current.scrollHeight,\n                behavior: smooth ? \"smooth\" : \"auto\"\n            });\n        }\n    };\n    const handleScroll = (e)=>{\n        const container = e.currentTarget;\n        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n        setShowScrollToBottom(!isNearBottom && messages.length > 0);\n    };\n    // Auto-scroll to bottom when new messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messages.length > 0) {\n            // Use requestAnimationFrame to ensure DOM has updated\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages.length\n    ]);\n    // Auto-scroll during streaming responses\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            // Scroll to bottom during streaming to show new content\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Auto-scroll when streaming content updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            const lastMessage = messages[messages.length - 1];\n            if (lastMessage && lastMessage.role === \"assistant\") {\n                // Scroll to bottom when assistant message content updates during streaming\n                requestAnimationFrame(()=>{\n                    scrollToBottom();\n                });\n            }\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Handle sidebar state changes to ensure proper centering\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Small delay to allow CSS transitions to complete\n        const timer = setTimeout(()=>{\n            if (messages.length > 0) {\n                // Maintain scroll position when sidebar toggles\n                requestAnimationFrame(()=>{\n                    if (messagesContainerRef.current) {\n                        const container = messagesContainerRef.current;\n                        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n                        if (isNearBottom) {\n                            scrollToBottom();\n                        }\n                    }\n                });\n            }\n        }, 200); // Match the transition duration\n        return ()=>clearTimeout(timer);\n    }, [\n        isCollapsed,\n        isHovered,\n        isHistoryCollapsed,\n        messages.length\n    ]);\n    // Prefetch chat history when hovering over configs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId && customConfigs.length > 0) {\n            // Prefetch chat history for other configs when user is idle\n            const otherConfigs = customConfigs.filter((config)=>config.id !== selectedConfigId).slice(0, 3); // Limit to 3 most recent other configs\n            const timer = setTimeout(()=>{\n                otherConfigs.forEach((config)=>{\n                    prefetchForNavigation(config.id);\n                });\n            }, 2000); // Wait 2 seconds before prefetching\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        selectedConfigId,\n        customConfigs,\n        prefetchForNavigation\n    ]);\n    // Load messages for a specific conversation with pagination\n    const loadConversation = async function(conversation) {\n        let loadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Set loading state for message loading\n        if (!loadMore) {\n            setIsLoadingMessages(true);\n        }\n        try {\n            // Note: isLoadingHistory is now managed by the useChatHistory hook\n            // For initial load, get latest 50 messages\n            // For load more, get older messages with offset\n            const limit = 50;\n            const offset = loadMore ? messages.length : 0;\n            const latest = !loadMore;\n            // Add cache-busting parameter to ensure fresh data after edits\n            const cacheBuster = Date.now();\n            const response = await fetch(\"/api/chat/messages?conversation_id=\".concat(conversation.id, \"&limit=\").concat(limit, \"&offset=\").concat(offset, \"&latest=\").concat(latest, \"&_cb=\").concat(cacheBuster), {\n                cache: \"no-store\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load conversation messages\");\n            }\n            const chatMessages = await response.json();\n            // Convert ChatMessage to PlaygroundMessage format\n            const playgroundMessages = chatMessages.map((msg)=>({\n                    id: msg.id,\n                    role: msg.role,\n                    content: msg.content.map((part)=>{\n                        var _part_image_url;\n                        if (part.type === \"text\" && part.text) {\n                            return {\n                                type: \"text\",\n                                text: part.text\n                            };\n                        } else if (part.type === \"image_url\" && ((_part_image_url = part.image_url) === null || _part_image_url === void 0 ? void 0 : _part_image_url.url)) {\n                            return {\n                                type: \"image_url\",\n                                image_url: {\n                                    url: part.image_url.url\n                                }\n                            };\n                        } else {\n                            // Fallback for malformed content\n                            return {\n                                type: \"text\",\n                                text: \"\"\n                            };\n                        }\n                    })\n                }));\n            if (loadMore) {\n                // Prepend older messages to the beginning\n                setMessages((prev)=>[\n                        ...playgroundMessages,\n                        ...prev\n                    ]);\n            } else {\n                // Replace all messages for initial load\n                setMessages(playgroundMessages);\n                // Note: currentConversation is now set optimistically in loadChatFromHistory\n                // Only set it here if it's not already set (for direct loadConversation calls)\n                if (!currentConversation || currentConversation.id !== conversation.id) {\n                    setCurrentConversation(conversation);\n                }\n            }\n            setError(null);\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        } finally{\n            // Clear loading state for message loading\n            if (!loadMore) {\n                setIsLoadingMessages(false);\n            }\n        // Note: isLoadingHistory is now managed by the useChatHistory hook\n        }\n    };\n    // Save current conversation\n    const saveConversation = async ()=>{\n        if (!selectedConfigId || messages.length === 0) return null;\n        try {\n            let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const firstMessage = messages[0];\n                let title = \"New Chat\";\n                if (firstMessage && firstMessage.content.length > 0) {\n                    const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                    if (textPart && textPart.text) {\n                        title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                    }\n                }\n                const newConversationData = {\n                    custom_api_config_id: selectedConfigId,\n                    title\n                };\n                const response = await fetch(\"/api/chat/conversations\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newConversationData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation\");\n                }\n                const newConversation = await response.json();\n                conversationId = newConversation.id;\n                setCurrentConversation(newConversation);\n            }\n            // Save all messages that aren't already saved\n            for (const message of messages){\n                // Check if message is already saved (has UUID format)\n                if (message.id.includes(\"-\") && message.id.length > 20) continue;\n                const newMessageData = {\n                    conversation_id: conversationId,\n                    role: message.role,\n                    content: message.content\n                };\n                await fetch(\"/api/chat/messages\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newMessageData)\n                });\n            }\n            // Only refresh chat history if we created a new conversation\n            if (!currentConversation) {\n                refetchChatHistory(true); // Force refresh for new conversations\n            }\n            return conversationId;\n        } catch (err) {\n            console.error(\"Error saving conversation:\", err);\n            setError(\"Failed to save conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Delete a conversation\n    const deleteConversation = async (conversationId)=>{\n        try {\n            const response = await fetch(\"/api/chat/conversations?id=\".concat(conversationId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete conversation\");\n            }\n            // If this was the current conversation, clear it\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n            // Force refresh chat history after deletion\n            refetchChatHistory(true);\n        } catch (err) {\n            console.error(\"Error deleting conversation:\", err);\n            setError(\"Failed to delete conversation: \".concat(err.message));\n        }\n    };\n    // Create a new conversation automatically when first message is sent\n    const createNewConversation = async (firstMessage)=>{\n        if (!selectedConfigId) return null;\n        try {\n            // Generate title from first message\n            let title = \"New Chat\";\n            if (firstMessage.content.length > 0) {\n                const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                if (textPart && textPart.text) {\n                    title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                }\n            }\n            const newConversationData = {\n                custom_api_config_id: selectedConfigId,\n                title\n            };\n            const response = await fetch(\"/api/chat/conversations\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newConversationData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to create conversation\");\n            }\n            const newConversation = await response.json();\n            setCurrentConversation(newConversation);\n            return newConversation.id;\n        } catch (err) {\n            console.error(\"Error creating conversation:\", err);\n            setError(\"Failed to create conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Save individual message to database\n    const saveMessageToDatabase = async (conversationId, message)=>{\n        try {\n            const newMessageData = {\n                conversation_id: conversationId,\n                role: message.role,\n                content: message.content\n            };\n            const response = await fetch(\"/api/chat/messages\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newMessageData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save message\");\n            }\n            return await response.json();\n        } catch (err) {\n            console.error(\"Error saving message:\", err);\n        // Don't show error to user for message saving failures\n        // The conversation will still work in the UI\n        }\n    };\n    const handleStarterClick = (prompt)=>{\n        setMessageInput(prompt);\n        // Auto-focus the input after setting the prompt\n        setTimeout(()=>{\n            const textarea = document.querySelector('textarea[placeholder*=\"Type a message\"]');\n            if (textarea) {\n                textarea.focus();\n                textarea.setSelectionRange(textarea.value.length, textarea.value.length);\n            }\n        }, 100);\n    };\n    const startNewChat = async ()=>{\n        // Save current conversation if it has messages\n        if (messages.length > 0) {\n            await saveConversation();\n        }\n        setMessages([]);\n        setCurrentConversation(null);\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Reset status tracking\n        messageStatus.reset();\n    };\n    // Handle model/router configuration change\n    const handleConfigChange = async (newConfigId)=>{\n        // Don't do anything if it's the same config\n        if (newConfigId === selectedConfigId) return;\n        // If there's an existing conversation with messages, start a new chat\n        if (messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [Model Switch] Starting new chat due to model change\");\n            await startNewChat();\n        }\n        // Update the selected configuration\n        setSelectedConfigId(newConfigId);\n        // Find the config name for logging\n        const selectedConfig = customConfigs.find((config)=>config.id === newConfigId);\n        const configName = selectedConfig ? selectedConfig.name : newConfigId;\n        console.log(\"\\uD83D\\uDD04 [Model Switch] Switched to config: \".concat(configName, \" (\").concat(newConfigId, \")\"));\n    };\n    const loadChatFromHistory = async (conversation)=>{\n        // Optimistic UI update - immediately switch to the selected conversation\n        console.log(\"\\uD83D\\uDD04 [INSTANT SWITCH] Immediately switching to conversation: \".concat(conversation.title));\n        // Clear current state immediately for instant feedback\n        setCurrentConversation(conversation);\n        setMessages([]); // Clear messages immediately to show loading state\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Save current conversation in background (non-blocking)\n        const savePromise = (async ()=>{\n            if (messages.length > 0 && !currentConversation) {\n                try {\n                    await saveConversation();\n                } catch (err) {\n                    console.error(\"Background save failed:\", err);\n                }\n            }\n        })();\n        // Load conversation messages in background\n        try {\n            await loadConversation(conversation);\n            console.log(\"✅ [INSTANT SWITCH] Successfully loaded conversation: \".concat(conversation.title));\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        // Don't revert currentConversation - keep the UI showing the selected conversation\n        }\n        // Ensure background save completes\n        await savePromise;\n    };\n    // Edit message functionality\n    const startEditingMessage = (messageId, currentText)=>{\n        setEditingMessageId(messageId);\n        setEditingText(currentText);\n    };\n    const cancelEditingMessage = ()=>{\n        setEditingMessageId(null);\n        setEditingText(\"\");\n    };\n    const saveEditedMessage = async ()=>{\n        if (!editingMessageId || !editingText.trim() || !selectedConfigId) return;\n        // Find the index of the message being edited\n        const messageIndex = messages.findIndex((msg)=>msg.id === editingMessageId);\n        if (messageIndex === -1) return;\n        // Update the message content\n        const updatedMessages = [\n            ...messages\n        ];\n        updatedMessages[messageIndex] = {\n            ...updatedMessages[messageIndex],\n            content: [\n                {\n                    type: \"text\",\n                    text: editingText.trim()\n                }\n            ]\n        };\n        // Remove all messages after the edited message (restart conversation from this point)\n        const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);\n        setMessages(messagesToKeep);\n        setEditingMessageId(null);\n        setEditingText(\"\");\n        // If we have a current conversation, update the database\n        if (currentConversation) {\n            try {\n                // Delete messages after the edited one from the database\n                const messagesToDelete = messages.slice(messageIndex + 1);\n                console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting \".concat(messagesToDelete.length, \" messages after edited message\"));\n                // Instead of trying to identify saved messages by ID format,\n                // delete all messages after the edited message's timestamp from the database\n                if (messagesToDelete.length > 0) {\n                    const editedMessage = messages[messageIndex];\n                    const editedMessageTimestamp = parseInt(editedMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting all messages after timestamp: \".concat(editedMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            after_timestamp: editedMessageTimestamp\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages after timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [EDIT MODE] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Update/save the edited message in the database\n                const editedMessage = messagesToKeep[messageIndex];\n                console.log(\"✏️ [EDIT MODE] Saving edited message with timestamp: \".concat(editedMessage.id));\n                // Use timestamp-based update to find and update the message\n                const updateResponse = await fetch(\"/api/chat/messages/update-by-timestamp\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        conversation_id: currentConversation.id,\n                        timestamp: parseInt(editedMessage.id),\n                        content: editedMessage.content\n                    })\n                });\n                if (!updateResponse.ok) {\n                    console.error(\"Failed to update message by timestamp:\", await updateResponse.text());\n                    // If update fails, try to save as new message (fallback)\n                    console.log(\"\\uD83D\\uDCDD [EDIT MODE] Fallback: Saving edited message as new message\");\n                    await saveMessageToDatabase(currentConversation.id, editedMessage);\n                } else {\n                    const result = await updateResponse.json();\n                    console.log(\"✅ [EDIT MODE] Successfully updated message: \".concat(result.message));\n                }\n                // Force refresh chat history to reflect changes and clear cache\n                refetchChatHistory(true);\n                // Also clear any message cache by adding a cache-busting parameter\n                if (true) {\n                    // Clear any cached conversation data\n                    const cacheKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"chat_\") || key.startsWith(\"conversation_\"));\n                    cacheKeys.forEach((key)=>localStorage.removeItem(key));\n                }\n            } catch (err) {\n                console.error(\"Error updating conversation:\", err);\n                setError(\"Failed to update conversation: \".concat(err.message));\n            }\n        }\n        // Now automatically send the edited message to get a response\n        await sendEditedMessageToAPI(messagesToKeep);\n    };\n    // Send the edited conversation to get a new response\n    const sendEditedMessageToAPI = async (conversationMessages)=>{\n        if (!selectedConfigId || conversationMessages.length === 0) return;\n        setIsLoading(true);\n        setError(null);\n        // Start status tracking for edit mode\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Sending edited conversation for new response...\");\n        // Prepare payload with the conversation up to the edited message\n        const messagesForPayload = conversationMessages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming\n        };\n        try {\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [EDIT MODE] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            if (useStreaming && response.body) {\n                // Handle streaming response with orchestration detection (same as handleSendMessage)\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [EDIT MODE] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Failed to parse stream chunk:\", parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                // Save the assistant response with auto-continuation support\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                        // Start auto-continuation after a brief delay\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, 2000);\n                    } else {\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                    }\n                }\n            } else {\n                var _data_choices__message, _data_choices_, _data_choices, _data_content_, _data_content;\n                // Handle non-streaming response\n                const data = await response.json();\n                let assistantContent = \"Could not parse assistant's response.\";\n                if ((_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content) {\n                    assistantContent = data.choices[0].message.content;\n                } else if ((_data_content = data.content) === null || _data_content === void 0 ? void 0 : (_data_content_ = _data_content[0]) === null || _data_content_ === void 0 ? void 0 : _data_content_.text) {\n                    assistantContent = data.content[0].text;\n                } else if (typeof data.text === \"string\") {\n                    assistantContent = data.text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save the assistant response\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Edit mode API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [EDIT MODE] Processing complete\");\n        }\n    };\n    // Handle retry message with optional specific API key\n    const handleRetryMessage = async (messageIndex, apiKeyId)=>{\n        if (!selectedConfigId || messageIndex < 0 || messageIndex >= messages.length) return;\n        const messageToRetry = messages[messageIndex];\n        if (messageToRetry.role !== \"assistant\") return;\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start status tracking for retry\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [RETRY] Retrying message with\", apiKeyId ? \"specific key: \".concat(apiKeyId) : \"same model\");\n        // Remove the assistant message and any messages after it\n        const messagesToKeep = messages.slice(0, messageIndex);\n        setMessages(messagesToKeep);\n        // If we have a current conversation, delete the retried message and subsequent ones from database\n        if (currentConversation) {\n            try {\n                const messagesToDelete = messages.slice(messageIndex);\n                console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting \".concat(messagesToDelete.length, \" messages from retry point\"));\n                // Delete all messages from the retry point onwards using timestamp-based deletion\n                if (messagesToDelete.length > 0) {\n                    const retryMessage = messages[messageIndex];\n                    const retryMessageTimestamp = parseInt(retryMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting all messages from timestamp: \".concat(retryMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            from_timestamp: retryMessageTimestamp // Use 'from' instead of 'after' to include the retry message\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages from timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [RETRY] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Refresh chat history to reflect changes\n                refetchChatHistory(true);\n            } catch (err) {\n                console.error(\"Error deleting retried messages:\", err);\n            }\n        }\n        // Prepare payload with messages up to the retry point\n        const messagesForPayload = messagesToKeep.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming,\n            ...apiKeyId && {\n                specific_api_key_id: apiKeyId\n            } // Add specific key if provided\n        };\n        try {\n            console.log(\"\\uD83D\\uDE80 [RETRY] Starting retry API call...\");\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [RETRY] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Check for orchestration headers in retry\n            const orchestrationId = response.headers.get(\"X-RoKey-Orchestration-ID\");\n            const orchestrationActive = response.headers.get(\"X-RoKey-Orchestration-Active\");\n            if (orchestrationId && orchestrationActive === \"true\") {\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration headers in retry - showing canvas\");\n                setOrchestrationExecutionId(orchestrationId);\n                setShowOrchestration(true);\n            }\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [RETRY] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            // Handle streaming or non-streaming response (reuse existing logic)\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let accumulatedText = \"\";\n                const currentAssistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const data = line.slice(6);\n                                if (data === \"[DONE]\") continue;\n                                try {\n                                    var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                    const parsed = JSON.parse(data);\n                                    if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                        const newContent = parsed.choices[0].delta.content;\n                                        accumulatedText += newContent;\n                                        // Detect orchestration content and update status dynamically\n                                        if (newContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || newContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || newContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || newContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || newContent.includes(\"Specialist:\")) {\n                                            console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                            messageStatus.markOrchestrationStarted();\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        } else if (orchestrationStatus) {\n                                            // Continue updating orchestration status if already in orchestration mode\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        }\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === currentAssistantMessage.id ? {\n                                                    ...msg,\n                                                    content: [\n                                                        {\n                                                            type: \"text\",\n                                                            text: accumulatedText\n                                                        }\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Failed to parse streaming chunk:\", parseError);\n                                }\n                            }\n                        }\n                    }\n                } finally{\n                    reader.releaseLock();\n                }\n                // Save final assistant message\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                }\n            } else {\n                // Non-streaming response\n                const data = await response.json();\n                let assistantContent = \"\";\n                if (data.choices && data.choices.length > 0 && data.choices[0].message) {\n                    assistantContent = data.choices[0].message.content;\n                } else if (data.content && Array.isArray(data.content) && data.content.length > 0) {\n                    assistantContent = data.content[0].text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save assistant message\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Retry API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-retry\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred during retry.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Save error message\n            if (currentConversation) {\n                await saveMessageToDatabase(currentConversation.id, errorMessage);\n            }\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [RETRY] Processing complete\");\n        }\n    };\n    const handleSendMessage = async (e)=>{\n        if (e) e.preventDefault();\n        // Allow sending if there's text OR images\n        if (!messageInput.trim() && imageFiles.length === 0 || !selectedConfigId) return;\n        // Check if this is a continuation request\n        const inputText = messageInput.trim().toLowerCase();\n        if (inputText === \"continue\" && messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [CONTINUE] Detected manual continuation request, routing to auto-continuation...\");\n            // Clear the input\n            setMessageInput(\"\");\n            // Route to auto-continuation instead of normal message flow\n            await handleAutoContinuation();\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start enhanced status tracking\n        messageStatus.startProcessing();\n        // Phase 1 Optimization: Performance tracking\n        const messagingStartTime = performance.now();\n        console.log(\"\\uD83D\\uDE80 [MESSAGING FLOW] Starting optimized parallel processing...\");\n        // Capture current input and images before clearing them\n        const currentMessageInput = messageInput.trim();\n        const currentImageFiles = [\n            ...imageFiles\n        ];\n        const currentImagePreviews = [\n            ...imagePreviews\n        ];\n        // Clear input and images immediately to prevent them from showing after send\n        setMessageInput(\"\");\n        handleRemoveImage();\n        const userMessageContentParts = [];\n        let apiMessageContentParts = []; // For the API payload, image_url.url will be base64\n        if (currentMessageInput) {\n            userMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n            apiMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n        }\n        // Process all images\n        if (currentImageFiles.length > 0) {\n            try {\n                for(let i = 0; i < currentImageFiles.length; i++){\n                    const file = currentImageFiles[i];\n                    const preview = currentImagePreviews[i];\n                    const base64ImageData = await fileToBase64(file);\n                    // For UI display (uses the preview which is already base64)\n                    userMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: preview\n                        }\n                    });\n                    // For API payload\n                    apiMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: base64ImageData\n                        }\n                    });\n                }\n            } catch (imgErr) {\n                console.error(\"Error converting images to base64:\", imgErr);\n                setError(\"Failed to process one or more images. Please try again.\");\n                setIsLoading(false);\n                // Restore the input and images if there was an error\n                setMessageInput(currentMessageInput);\n                setImageFiles(currentImageFiles);\n                setImagePreviews(currentImagePreviews);\n                return;\n            }\n        }\n        const newUserMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: userMessageContentParts\n        };\n        setMessages((prevMessages)=>[\n                ...prevMessages,\n                newUserMessage\n            ]);\n        // Phase 1 Optimization: Start conversation creation and user message saving in background\n        // Don't wait for these operations - they can happen in parallel with LLM call\n        let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        let conversationPromise = Promise.resolve(conversationId);\n        let userMessageSavePromise = Promise.resolve();\n        if (!conversationId && !currentConversation) {\n            console.log(\"\\uD83D\\uDD04 [PARALLEL] Starting conversation creation in background...\");\n            conversationPromise = createNewConversation(newUserMessage);\n        }\n        // Start user message saving in background (will wait for conversation if needed)\n        userMessageSavePromise = conversationPromise.then(async (convId)=>{\n            if (convId) {\n                console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving user message in background...\");\n                await saveMessageToDatabase(convId, newUserMessage);\n                console.log(\"✅ [PARALLEL] User message saved\");\n                return convId;\n            }\n        }).catch((err)=>{\n            console.error(\"❌ [PARALLEL] User message save failed:\", err);\n        });\n        // Prepare payload.messages by transforming existing messages and adding the new one\n        const existingMessagesForPayload = messages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                // System messages are always simple text strings\n                // Their content in PlaygroundMessage is [{type: 'text', text: 'Actual system prompt'}]\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\"; // Fallback, though system messages should always be text\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                // Single text part for user/assistant, send as string for API\n                contentForApi = m.content[0].text;\n            } else {\n                // Multimodal content (e.g., user message with image) or multiple parts\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        // The part.image_url.url from messages state is the base64 data URL (preview)\n                        // This is what we want to send to the backend.\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    // Ensure it's properly cast for text part before accessing .text\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: [\n                ...existingMessagesForPayload,\n                {\n                    role: \"user\",\n                    content: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === \"text\" ? apiMessageContentParts[0].text : apiMessageContentParts\n                }\n            ],\n            stream: useStreaming\n        };\n        try {\n            // Phase 1 Optimization: Start LLM call immediately in parallel with background operations\n            console.log(\"\\uD83D\\uDE80 [PARALLEL] Starting LLM API call...\");\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                // Conservative performance optimizations\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [PARALLEL] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Check for orchestration headers\n            const orchestrationId = response.headers.get(\"X-RoKey-Orchestration-ID\");\n            const orchestrationActive = response.headers.get(\"X-RoKey-Orchestration-Active\");\n            console.log(\"\\uD83C\\uDFAD [DEBUG] Checking orchestration headers:\", {\n                orchestrationId,\n                orchestrationActive,\n                allHeaders: Object.fromEntries(response.headers.entries())\n            });\n            if (orchestrationId && orchestrationActive === \"true\") {\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration headers - showing canvas\");\n                setOrchestrationExecutionId(orchestrationId);\n                setShowOrchestration(true);\n            } else {\n                console.log(\"\\uD83C\\uDFAD [DEBUG] No orchestration headers found or not active\");\n            }\n            // If we're here, it's a stream.\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Playground: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                if (accumulatedText) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check response headers to determine if this is chunked synthesis\n                    const synthesisProgress = response.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = response.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                        // For chunked synthesis, start continuation immediately\n                        // For regular synthesis, add a delay\n                        const delay = isChunkedSynthesis ? 1000 : 2000;\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, delay);\n                    } else {\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                    }\n                }\n            }\n        } catch (err) {\n            console.error(\"Playground API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Phase 1 Optimization: Save error message in background\n            conversationPromise.then(async (convId)=>{\n                if (convId) {\n                    console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving error message in background...\");\n                    await saveMessageToDatabase(convId, errorMessage);\n                    console.log(\"✅ [PARALLEL] Error message saved\");\n                }\n            }).catch((saveErr)=>{\n                console.error(\"❌ [PARALLEL] Error message save failed:\", saveErr);\n            });\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.logStatusPerformance)(messageStatus.stageHistory);\n            // Phase 1 Optimization: Performance summary\n            const totalMessagingTime = performance.now() - messagingStartTime;\n            console.log(\"\\uD83D\\uDCCA [MESSAGING FLOW] Total time: \".concat(totalMessagingTime.toFixed(1), \"ms\"));\n            // Phase 1 Optimization: Refresh chat history in background, don't block UI\n            conversationPromise.then(async (convId)=>{\n                if (convId && !currentConversation) {\n                    console.log(\"\\uD83D\\uDD04 [PARALLEL] Refreshing chat history in background...\");\n                    refetchChatHistory(true);\n                    console.log(\"✅ [PARALLEL] Chat history refreshed\");\n                }\n            }).catch((refreshErr)=>{\n                console.error(\"❌ [PARALLEL] Chat history refresh failed:\", refreshErr);\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col transition-all duration-300 ease-in-out\",\n                style: {\n                    marginLeft: sidebarWidth,\n                    marginRight: isCanvasOpen && !isCanvasMinimized ? \"50%\" // Canvas takes 50% of screen width\n                     : isHistoryCollapsed ? \"0px\" : \"320px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isCanvasOpen && !isCanvasMinimized ? \"50%\" // Canvas takes 50% of screen width\n                             : isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: selectedConfigId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2010,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2011,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2015,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"Not Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2016,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2007,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>handleConfigChange(e.target.value),\n                                                        disabled: customConfigs.length === 0,\n                                                        className: \"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Router\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2027,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2029,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2021,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2036,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2035,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2034,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2020,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2006,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Streaming\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2044,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setUseStreaming(!useStreaming),\n                                                className: \"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm \".concat(useStreaming ? \"bg-orange-500 shadow-orange-200\" : \"bg-gray-300\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm \".concat(useStreaming ? \"translate-x-6\" : \"translate-x-1\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2051,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2045,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2043,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2004,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2003,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 1997,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col pt-20 pb-32\",\n                        children: messages.length === 0 && !currentConversation ? /* Welcome Screen - Perfectly centered with no scroll */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center px-6 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mx-auto transition-all duration-300 \".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-2xl\" : \"max-w-4xl\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Welcome to RoKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2072,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600 max-w-md mx-auto\",\n                                                    children: \"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2073,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2071,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 w-full max-w-2xl\",\n                                            children: conversationStarters.map((starter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleStarterClick(starter.prompt),\n                                                    disabled: !selectedConfigId,\n                                                    className: \"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed \".concat(!selectedConfigId ? \"cursor-not-allowed\" : \"cursor-pointer hover:scale-[1.02]\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-xl flex items-center justify-center text-xl \".concat(starter.color, \" group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: starter.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2090,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors\",\n                                                                            children: starter.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2094,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                            children: starter.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2097,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2093,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2089,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-orange-500\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 4l8 8-8 8M4 12h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2104,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2103,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2102,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, starter.id, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2081,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2079,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2070,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2067,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2066,\n                            columnNumber: 13\n                        }, this) : /* Chat Messages - Scrollable area with perfect centering */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesContainerRef,\n                                        className: \"w-full h-full overflow-y-auto px-6 transition-all duration-300 \".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-2xl\" : \"max-w-4xl\"),\n                                        onScroll: handleScroll,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 py-8\",\n                                            children: [\n                                                currentConversation && messages.length >= 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>loadConversation(currentConversation, true),\n                                                        disabled: isLoadingHistory,\n                                                        className: \"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50\",\n                                                        children: isLoadingHistory ? \"Loading...\" : \"Load Earlier Messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2128,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2127,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoadingMessages && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: Array.from({\n                                                        length: 3\n                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2143,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2146,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2147,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-5/6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2148,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2145,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2144,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2142,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2140,\n                                                    columnNumber: 23\n                                                }, this),\n                                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\", \" group\"),\n                                                        children: [\n                                                            msg.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-orange-500\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2164,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2163,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2162,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-[80%]\" : \"max-w-[65%]\", \" relative \").concat(msg.role === \"user\" ? \"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm\" : msg.role === \"assistant\" ? \"card text-gray-900 rounded-2xl rounded-bl-lg\" : msg.role === \"system\" ? \"bg-amber-50 text-amber-800 rounded-xl border border-amber-200\" : \"bg-red-50 text-red-800 rounded-xl border border-red-200\", \" px-4 py-3 transition-all duration-300\"),\n                                                                children: [\n                                                                    msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\",\n                                                                                className: \"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2184,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditingMessage(msg.id, msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\")),\n                                                                                className: \"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer\",\n                                                                                title: \"Edit message\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"w-4 h-4 stroke-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2196,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2191,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2183,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    msg.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-8 left-0 z-10 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2204,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            msg.role === \"assistant\" && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                configId: selectedConfigId,\n                                                                                onRetry: (apiKeyId)=>handleRetryMessage(index, apiKeyId),\n                                                                                disabled: isLoading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2211,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2203,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2 chat-message-content\",\n                                                                        children: msg.role === \"user\" && editingMessageId === msg.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: editingText,\n                                                                                    onChange: (e)=>setEditingText(e.target.value),\n                                                                                    className: \"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none\",\n                                                                                    placeholder: \"Edit your message...\",\n                                                                                    rows: 3,\n                                                                                    autoFocus: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2224,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: saveEditedMessage,\n                                                                                            disabled: !editingText.trim(),\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2238,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Save & Continue\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2239,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2233,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: cancelEditingMessage,\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2245,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2246,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2241,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2232,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white/70 text-xs\",\n                                                                                    children: \"\\uD83D\\uDCA1 Saving will restart the conversation from this point, removing all messages that came after.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2249,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2223,\n                                                                            columnNumber: 23\n                                                                        }, this) : /* Normal message display */ msg.content.map((part, partIndex)=>{\n                                                                            if (part.type === \"text\") {\n                                                                                // Use LazyMarkdownRenderer for assistant messages, plain text for others\n                                                                                if (msg.role === \"assistant\") {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                        content: part.text,\n                                                                                        className: \"text-sm\"\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2260,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                } else {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"whitespace-pre-wrap break-words leading-relaxed text-sm\",\n                                                                                        children: part.text\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2268,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                }\n                                                                            }\n                                                                            if (part.type === \"image_url\") {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: part.image_url.url,\n                                                                                    alt: \"uploaded content\",\n                                                                                    className: \"max-w-full max-h-48 rounded-xl shadow-sm\"\n                                                                                }, partIndex, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2276,\n                                                                                    columnNumber: 29\n                                                                                }, this);\n                                                                            }\n                                                                            return null;\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2220,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2169,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-white\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2293,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2292,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2291,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, msg.id, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2157,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    currentStage: messageStatus.currentStage,\n                                                    isStreaming: useStreaming && messageStatus.currentStage === \"typing\",\n                                                    orchestrationStatus: orchestrationStatus,\n                                                    onStageChange: (stage)=>{\n                                                        console.log(\"\\uD83C\\uDFAF UI Status changed to: \".concat(stage));\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2301,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showOrchestration && orchestrationExecutionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OrchestrationCanvas__WEBPACK_IMPORTED_MODULE_6__.OrchestrationCanvas, {\n                                                    executionId: orchestrationExecutionId,\n                                                    onCanvasStateChange: handleCanvasStateChange,\n                                                    onComplete: (result)=>{\n                                                        // Skip auto-completion for test execution IDs\n                                                        if (orchestrationExecutionId === null || orchestrationExecutionId === void 0 ? void 0 : orchestrationExecutionId.startsWith(\"test-execution-id\")) {\n                                                            console.log(\"\\uD83C\\uDFAD [DEBUG] Skipping auto-completion for test execution\");\n                                                            return;\n                                                        }\n                                                        console.log(\"\\uD83C\\uDF89 [ORCHESTRATION] Completed:\", result);\n                                                        // Add the final result as a message\n                                                        const finalMessage = {\n                                                            id: Date.now().toString() + \"-orchestration-final\",\n                                                            role: \"assistant\",\n                                                            content: [\n                                                                {\n                                                                    type: \"text\",\n                                                                    text: result\n                                                                }\n                                                            ]\n                                                        };\n                                                        setMessages((prevMessages)=>[\n                                                                ...prevMessages,\n                                                                finalMessage\n                                                            ]);\n                                                        // Hide orchestration UI\n                                                        setShowOrchestration(false);\n                                                        setOrchestrationExecutionId(null);\n                                                        // Save final message\n                                                        if (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) {\n                                                            saveMessageToDatabase(currentConversation.id, finalMessage).catch((err)=>{\n                                                                console.error(\"❌ Failed to save orchestration final message:\", err);\n                                                            });\n                                                        }\n                                                    },\n                                                    onError: (error)=>{\n                                                        // Skip auto-close for test execution IDs\n                                                        if (orchestrationExecutionId === null || orchestrationExecutionId === void 0 ? void 0 : orchestrationExecutionId.startsWith(\"test-execution-id\")) {\n                                                            console.log(\"\\uD83C\\uDFAD [DEBUG] Ignoring test execution error:\", error);\n                                                            return;\n                                                        }\n                                                        console.error(\"❌ [ORCHESTRATION] Error:\", error);\n                                                        setError(\"Orchestration error: \".concat(error));\n                                                        setShowOrchestration(false);\n                                                        setOrchestrationExecutionId(null);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2313,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2360,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2124,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2117,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2116,\n                                    columnNumber: 15\n                                }, this),\n                                showScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToBottom(true),\n                                        className: \"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group\",\n                                        \"aria-label\": \"Scroll to bottom\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2374,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2373,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2368,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2367,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2115,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2063,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 z-50 bg-[#faf8f5]/95 backdrop-blur-sm border-t border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isCanvasOpen && !isCanvasMinimized ? \"50%\" // Canvas takes 50% of screen width\n                             : isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full transition-all duration-300 \".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-2xl\" : \"max-w-4xl\"),\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-red-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2399,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2398,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800 text-sm font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2401,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2397,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2396,\n                                        columnNumber: 17\n                                    }, this),\n                                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] Manual canvas test triggered\");\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] Current state before:\", {\n                                                        showOrchestration,\n                                                        orchestrationExecutionId\n                                                    });\n                                                    setOrchestrationExecutionId(\"test-execution-id-\" + Date.now());\n                                                    setShowOrchestration(true);\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] State should be updated now\");\n                                                    // Also test if OrchestrationCanvas component exists\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] OrchestrationCanvas component:\", _components_OrchestrationCanvas__WEBPACK_IMPORTED_MODULE_6__.OrchestrationCanvas);\n                                                },\n                                                className: \"px-4 py-2 bg-purple-500 text-white rounded-lg text-sm hover:bg-purple-600\",\n                                                children: \"\\uD83C\\uDFAD Test Canvas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2409,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] Manual canvas close triggered\");\n                                                    setShowOrchestration(false);\n                                                    setOrchestrationExecutionId(null);\n                                                },\n                                                className: \"px-4 py-2 bg-red-500 text-white rounded-lg text-sm hover:bg-red-600\",\n                                                children: \"❌ Close Canvas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2424,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2408,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSendMessage,\n                                        children: [\n                                            imagePreviews.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2443,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            imagePreviews.length,\n                                                                            \" image\",\n                                                                            imagePreviews.length > 1 ? \"s\" : \"\",\n                                                                            \" attached\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2444,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2442,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>handleRemoveImage(),\n                                                                className: \"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium\",\n                                                                children: \"Clear all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2448,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2441,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3\",\n                                                        children: imagePreviews.map((preview, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: preview,\n                                                                                alt: \"Preview \".concat(index + 1),\n                                                                                className: \"w-full h-full object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2460,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2465,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>handleRemoveImage(index),\n                                                                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100\",\n                                                                                \"aria-label\": \"Remove image \".concat(index + 1),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"w-3.5 h-3.5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2472,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2466,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2459,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2475,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2458,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2456,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2440,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-4 space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            multiple: true,\n                                                            onChange: handleImageChange,\n                                                            ref: fileInputRef,\n                                                            className: \"hidden\",\n                                                            id: \"imageUpload\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2488,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            disabled: imageFiles.length >= 10,\n                                                            className: \"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 \".concat(imageFiles.length >= 10 ? \"text-gray-300 cursor-not-allowed\" : \"text-gray-400 hover:text-orange-500 hover:bg-orange-50\"),\n                                                            \"aria-label\": imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images\",\n                                                            title: imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images (up to 10)\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2511,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                imageFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold\",\n                                                                    children: imageFiles.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2513,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2499,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: messageInput,\n                                                                onChange: (e)=>setMessageInput(e.target.value),\n                                                                placeholder: selectedConfigId ? \"Type a message...\" : \"Select a router first\",\n                                                                disabled: !selectedConfigId || isLoading,\n                                                                rows: 1,\n                                                                className: \"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                        e.preventDefault();\n                                                                        if ((messageInput.trim() || imageFiles.length > 0) && selectedConfigId && !isLoading) {\n                                                                            handleSendMessage();\n                                                                        }\n                                                                    }\n                                                                },\n                                                                style: {\n                                                                    minHeight: \"24px\",\n                                                                    maxHeight: \"120px\"\n                                                                },\n                                                                onInput: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.height = \"auto\";\n                                                                    target.style.height = Math.min(target.scrollHeight, 120) + \"px\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2521,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2520,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: !selectedConfigId || isLoading || !messageInput.trim() && imageFiles.length === 0,\n                                                            className: \"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0\",\n                                                            \"aria-label\": \"Send message\",\n                                                            title: \"Send message\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 animate-spin\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2555,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2554,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2558,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2546,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2486,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2437,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2391,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2390,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2384,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 1990,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 \".concat(isHistoryCollapsed ? \"w-0 overflow-hidden\" : \"w-80\"),\n                style: {\n                    transform: isHistoryCollapsed ? \"translateX(100%)\" : \"translateX(0)\",\n                    opacity: isHistoryCollapsed ? 0 : 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-orange-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2584,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2583,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2582,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2588,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        chatHistory.length,\n                                                        \" conversations\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2589,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2587,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2581,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsHistoryCollapsed(!isHistoryCollapsed),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105\",\n                                    \"aria-label\": \"Toggle history sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2598,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2597,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2592,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2580,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startNewChat,\n                                className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2610,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2609,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"New Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2612,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2605,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2604,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                            children: isLoadingHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-xl border border-gray-100 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-4 w-3/4 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2623,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-3 w-1/2 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2624,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2622,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2620,\n                                columnNumber: 15\n                            }, this) : chatHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2632,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2631,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2630,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2635,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Start chatting to see your history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2636,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2629,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: chatHistory.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatHistoryItem, {\n                                        chat: chat,\n                                        currentConversation: currentConversation,\n                                        onLoadChat: loadChatFromHistory,\n                                        onDeleteChat: deleteConversation\n                                    }, chat.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2641,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2618,\n                            columnNumber: 11\n                        }, this),\n                        isChatHistoryStale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-orange-50 border-t border-orange-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-xs text-orange-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 mr-1 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2658,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2657,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Updating...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2656,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2655,\n                            columnNumber: 13\n                        }, this),\n                        chatHistoryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-red-50 border-t border-red-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Failed to load history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2669,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>refetchChatHistory(true),\n                                        className: \"text-red-700 hover:text-red-800 font-medium\",\n                                        children: \"Retry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2670,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2668,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2667,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2578,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2572,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out \".concat(isHistoryCollapsed ? \"opacity-100 scale-100 translate-x-0\" : \"opacity-0 scale-95 translate-x-4 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsHistoryCollapsed(false),\n                    className: \"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105\",\n                    \"aria-label\": \"Show history sidebar\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2692,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2691,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2686,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2683,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 1988,\n        columnNumber: 5\n    }, this);\n}\n_s(PlaygroundPage, \"R4rSfkZ/ZTg26ncZ8eLiZPtzFLM=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar,\n        _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch\n    ];\n});\n_c1 = PlaygroundPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatHistoryItem\");\n$RefreshReg$(_c1, \"PlaygroundPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/playground/page.tsx\n"));

/***/ })

});