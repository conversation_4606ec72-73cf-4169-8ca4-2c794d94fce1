"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"a8fda08377a1\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTAxNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE4ZmRhMDgzNzdhMVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/SidebarContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/SidebarContext.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SidebarProvider: function() { return /* binding */ SidebarProvider; },\n/* harmony export */   useSidebar: function() { return /* binding */ useSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SidebarProvider,useSidebar auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst SidebarContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction SidebarProvider(param) {\n    let { children } = param;\n    _s();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Always start collapsed\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHoverDisabled, setIsHoverDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Remove localStorage persistence for hover-based sidebar\n    // The sidebar will always start collapsed and expand on hover\n    const toggleSidebar = ()=>setIsCollapsed(!isCollapsed);\n    const collapseSidebar = ()=>setIsCollapsed(true);\n    const expandSidebar = ()=>setIsCollapsed(false);\n    const setHovered = (hovered)=>{\n        // Don't allow hover if disabled\n        if (!isHoverDisabled) {\n            setIsHovered(hovered);\n        }\n    };\n    const setHoverDisabled = (disabled)=>{\n        setIsHoverDisabled(disabled);\n        // If disabling hover, also clear current hover state\n        if (disabled) {\n            setIsHovered(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContext.Provider, {\n        value: {\n            isCollapsed,\n            isHovered,\n            isHoverDisabled,\n            toggleSidebar,\n            collapseSidebar,\n            expandSidebar,\n            setHovered,\n            setHoverDisabled\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\contexts\\\\SidebarContext.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(SidebarProvider, \"rmam1v+FODMMz2dsoVb+qJMsAcQ=\");\n_c = SidebarProvider;\nfunction useSidebar() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SidebarContext);\n    if (context === undefined) {\n        throw new Error(\"useSidebar must be used within a SidebarProvider\");\n    }\n    return context;\n}\n_s1(useSidebar, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"SidebarProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/SidebarContext.tsx\n"));

/***/ })

});