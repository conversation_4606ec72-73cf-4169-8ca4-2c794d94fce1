'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChatMessage } from './ChatMessage';
import { TypingIndicator } from './TypingIndicator';
import {
  generateConversationalMessage,
  generateSpecialistPersonalityMessage,
  generateModeratorClarification
} from '@/utils/orchestrationUtils';

interface OrchestrationEvent {
  type: string;
  role_id?: string;
  model_name?: string;
  data?: any;
  timestamp?: string;
}

interface OrchestrationChatroomProps {
  executionId: string;
  events: OrchestrationEvent[];
  isConnected: boolean;
  error: string | null;
  isComplete: boolean;
}

interface ChatMessageData {
  id: string;
  sender: 'moderator' | 'specialist';
  senderName: string;
  roleId?: string;
  content: string;
  timestamp: Date;
  type: 'message' | 'assignment' | 'handoff' | 'clarification' | 'completion';
}

export const OrchestrationChatroom: React.FC<OrchestrationChatroomProps> = ({
  executionId,
  events,
  isConnected,
  error,
  isComplete
}) => {
  const [chatMessages, setChatMessages] = useState<ChatMessageData[]>([]);
  const [typingSpecialists, setTypingSpecialists] = useState<Set<string>>(new Set());
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Debug logging
  useEffect(() => {
    console.log('🎭 [CHATROOM DEBUG] State:', {
      executionId,
      eventsCount: events.length,
      isConnected,
      error,
      isComplete,
      events: events.slice(0, 3) // Show first 3 events
    });
  }, [executionId, events, isConnected, error, isComplete]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  // Add simulated interactive events for testing
  useEffect(() => {
    if (events.length > 0 && !chatMessages.some(msg => msg.type === 'clarification')) {
      // Add some simulated clarification events for testing
      const simulatedEvents: OrchestrationEvent[] = [
        ...events,
        // Add a clarification question after the first assignment
        {
          type: 'specialist_clarification',
          role_id: 'game-designer',
          data: { message: null },
          timestamp: new Date(Date.now() + 2000).toISOString()
        }
      ];

      // Use simulated events for now to test the enhanced conversations
      processEvents(simulatedEvents);
    } else {
      processEvents(events);
    }
  }, [events, executionId]);

  // Convert orchestration events to chat messages
  const processEvents = (eventsToProcess: OrchestrationEvent[]) => {
    const newMessages: ChatMessageData[] = [];
    const currentlyTyping = new Set<string>();

    eventsToProcess.forEach((event, index) => {
      const timestamp = new Date(event.timestamp || Date.now());
      const messageId = `${executionId}-${index}`;

      switch (event.type) {
        case 'orchestration_started':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: generateConversationalMessage('orchestration_started'),
            timestamp,
            type: 'message'
          });
          break;

        case 'moderator_introduction':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'AI Moderator',
            content: event.data?.message || 'Hello team! Let me introduce everyone...',
            timestamp,
            type: 'message'
          });
          break;

        case 'specialist_introduction':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'AI Moderator',
            content: event.data?.message || `Introducing ${event.data?.specialist} specialist...`,
            timestamp,
            type: 'assignment'
          });
          break;

        case 'moderator_assignment':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'AI Moderator',
            content: event.data?.message || `Assignment given to ${event.data?.target_specialist}`,
            timestamp,
            type: 'assignment'
          });
          break;

        case 'task_decomposed':
          const steps = event.data?.steps || [];
          const teamIntro = steps.map((step: any) =>
            `🤖 @${step.roleId} - ${step.modelName || 'AI Specialist'}`
          ).join('\n');

          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: `${generateConversationalMessage('task_decomposed')}\n\n${teamIntro}\n\nLet's begin the collaboration!`,
            timestamp,
            type: 'assignment'
          });
          break;

        case 'step_assigned':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            roleId: event.role_id,
            content: generateModeratorClarification(event.role_id || 'specialist', '', 'assignment'),
            timestamp,
            type: 'assignment'
          });
          break;

        case 'moderator_assignment':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            roleId: event.role_id,
            content: event.data?.message || generateConversationalMessage('moderator_assignment', event.role_id),
            timestamp,
            type: 'assignment'
          });
          break;

        case 'specialist_acknowledgment':
          newMessages.push({
            id: messageId,
            sender: 'specialist',
            senderName: event.data?.speaker?.replace('_', ' ').toUpperCase() || 'Specialist',
            roleId: event.data?.speaker,
            content: event.data?.message || generateSpecialistPersonalityMessage(event.data?.speaker || 'specialist', 'acknowledgment'),
            timestamp,
            type: 'message'
          });
          break;

        case 'specialist_clarification':
          newMessages.push({
            id: messageId,
            sender: 'specialist',
            senderName: event.role_id || 'Specialist',
            roleId: event.role_id,
            content: event.data?.message || generateSpecialistPersonalityMessage(event.role_id || 'specialist', 'clarification'),
            timestamp,
            type: 'clarification'
          });
          break;

        case 'specialist_working':
          newMessages.push({
            id: messageId,
            sender: 'specialist',
            senderName: event.data?.speaker?.replace('_', ' ').toUpperCase() || 'Specialist',
            roleId: event.data?.speaker,
            content: event.data?.message || '🔄 Analyzing the requirements...',
            timestamp,
            type: 'message'
          });
          // Add to typing indicators
          if (event.data?.speaker) {
            currentlyTyping.add(event.data.speaker);
          }
          break;

        case 'specialist_response':
          newMessages.push({
            id: messageId,
            sender: 'specialist',
            senderName: event.data?.speaker?.replace('_', ' ').toUpperCase() || 'Specialist',
            roleId: event.data?.speaker,
            content: event.data?.message || 'Task completed successfully!',
            timestamp,
            type: 'completion'
          });
          // Remove from typing indicators
          if (event.data?.speaker) {
            currentlyTyping.delete(event.data.speaker);
          }
          break;

        case 'moderator_handoff':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'AI Moderator',
            content: event.data?.message || `Handing off to next specialist...`,
            timestamp,
            type: 'handoff'
          });
          break;

        case 'moderator_synthesis_start':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'AI Moderator',
            content: event.data?.message || 'Starting synthesis of all specialist contributions...',
            timestamp,
            type: 'message'
          });
          currentlyTyping.add('moderator');
          break;

        case 'synthesis_progress':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'AI Moderator',
            content: event.data?.message || '🧩 Weaving together specialist insights...',
            timestamp,
            type: 'message'
          });
          break;

        case 'orchestration_complete':
          currentlyTyping.delete('moderator');
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'AI Moderator',
            content: event.data?.message || '🎉 Team collaboration complete!',
            timestamp,
            type: 'completion'
          });
          break;

        case 'step_started':
          // Add to typing indicators
          if (event.role_id) {
            currentlyTyping.add(event.role_id);
          }
          break;

        case 'step_progress':
          if (event.role_id) {
            currentlyTyping.add(event.role_id);
          }
          break;

        case 'specialist_message':
          const completionMessage = event.data?.message || generateSpecialistPersonalityMessage(event.role_id || 'specialist', 'completion');
          newMessages.push({
            id: messageId,
            sender: 'specialist',
            senderName: event.role_id || 'Specialist',
            roleId: event.role_id,
            content: `${completionMessage}\n\n${event.data?.output || 'Task completed successfully!'}`,
            timestamp,
            type: 'completion'
          });
          break;

        case 'step_completed':
          // Remove from typing
          if (event.role_id) {
            currentlyTyping.delete(event.role_id);
          }
          break;

        case 'handoff_message':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: event.data?.message || generateConversationalMessage('handoff_message', event.data?.fromRole),
            timestamp,
            type: 'handoff'
          });
          break;

        case 'synthesis_started':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: event.data?.message || generateConversationalMessage('synthesis_started'),
            timestamp,
            type: 'message'
          });
          currentlyTyping.add('moderator');
          break;

        case 'synthesis_complete':
          currentlyTyping.delete('moderator');
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: event.data?.message || generateConversationalMessage('synthesis_complete'),
            timestamp,
            type: 'completion'
          });
          break;
      }
    });

    setChatMessages(newMessages);
    setTypingSpecialists(currentlyTyping);
  };

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Connection Error</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Connection Status */}
      <div className={`px-4 py-2 text-xs font-medium ${
        isConnected 
          ? 'bg-green-50 text-green-700 border-b border-green-100' 
          : 'bg-yellow-50 text-yellow-700 border-b border-yellow-100'
      }`}>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-yellow-500'}`} />
          <span>{isConnected ? 'Connected to AI Team' : 'Connecting...'}</span>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {chatMessages.length === 0 && (
          <div className="text-center py-8">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-blue-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <p className="text-gray-500">Waiting for AI team to start collaboration...</p>
          </div>
        )}

        {chatMessages.map((message) => (
          <ChatMessage
            key={message.id}
            message={message}
          />
        ))}

        {/* Typing Indicators */}
        {Array.from(typingSpecialists).map((specialist) => (
          <TypingIndicator
            key={specialist}
            senderName={specialist}
            roleId={specialist !== 'moderator' ? specialist : undefined}
          />
        ))}

        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};
