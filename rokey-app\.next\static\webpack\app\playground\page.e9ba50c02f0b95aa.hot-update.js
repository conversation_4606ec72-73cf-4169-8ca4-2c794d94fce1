"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationChatroom.tsx":
/*!**************************************************!*\
  !*** ./src/components/OrchestrationChatroom.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationChatroom: function() { return /* binding */ OrchestrationChatroom; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatMessage */ \"(app-pages-browser)/./src/components/ChatMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* harmony import */ var _utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/orchestrationUtils */ \"(app-pages-browser)/./src/utils/orchestrationUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationChatroom auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationChatroom = (param)=>{\n    let { executionId, events, isConnected, error, isComplete } = param;\n    _s();\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingSpecialists, setTypingSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDFAD [CHATROOM DEBUG] State:\", {\n            executionId,\n            eventsCount: events.length,\n            isConnected,\n            error,\n            isComplete,\n            events: events.slice(0, 3) // Show first 3 events\n        });\n    }, [\n        executionId,\n        events,\n        isConnected,\n        error,\n        isComplete\n    ]);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        chatMessages\n    ]);\n    // Add simulated interactive events for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (events.length > 0 && !chatMessages.some((msg)=>msg.type === \"clarification\")) {\n            // Add some simulated clarification events for testing\n            const simulatedEvents = [\n                ...events,\n                // Add a clarification question after the first assignment\n                {\n                    type: \"specialist_clarification\",\n                    role_id: \"game-designer\",\n                    data: {\n                        message: null\n                    },\n                    timestamp: new Date(Date.now() + 2000).toISOString()\n                }\n            ];\n            // Use simulated events for now to test the enhanced conversations\n            processEvents(simulatedEvents);\n        } else {\n            processEvents(events);\n        }\n    }, [\n        events,\n        executionId\n    ]);\n    // Convert orchestration events to chat messages\n    const processEvents = (eventsToProcess)=>{\n        const newMessages = [];\n        const currentlyTyping = new Set();\n        eventsToProcess.forEach((event, index)=>{\n            const timestamp = new Date(event.timestamp || Date.now());\n            const messageId = \"\".concat(executionId, \"-\").concat(index);\n            switch(event.type){\n                case \"orchestration_started\":\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"orchestration_started\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"moderator_introduction\":\n                    var _event_data;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.message) || \"Hello team! Let me introduce everyone...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"specialist_introduction\":\n                    var _event_data1, _event_data2;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.message) || \"Introducing \".concat((_event_data2 = event.data) === null || _event_data2 === void 0 ? void 0 : _event_data2.specialist, \" specialist...\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"moderator_assignment\":\n                    var _event_data3, _event_data4;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data3 = event.data) === null || _event_data3 === void 0 ? void 0 : _event_data3.message) || \"Assignment given to \".concat((_event_data4 = event.data) === null || _event_data4 === void 0 ? void 0 : _event_data4.target_specialist),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"task_decomposed\":\n                    var _event_data5;\n                    const steps = ((_event_data5 = event.data) === null || _event_data5 === void 0 ? void 0 : _event_data5.steps) || [];\n                    const teamIntro = steps.map((step)=>\"\\uD83E\\uDD16 @\".concat(step.roleId, \" - \").concat(step.modelName || \"AI Specialist\")).join(\"\\n\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: \"\".concat((0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"task_decomposed\"), \"\\n\\n\").concat(teamIntro, \"\\n\\nLet's begin the collaboration!\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"step_assigned\":\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateModeratorClarification)(event.role_id || \"specialist\", \"\", \"assignment\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"moderator_assignment\":\n                    var _event_data6;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: ((_event_data6 = event.data) === null || _event_data6 === void 0 ? void 0 : _event_data6.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"moderator_assignment\", event.role_id),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"specialist_acknowledgment\":\n                    var _event_data7;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: ((_event_data7 = event.data) === null || _event_data7 === void 0 ? void 0 : _event_data7.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateSpecialistPersonalityMessage)(event.role_id || \"specialist\", \"acknowledgment\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"specialist_clarification\":\n                    var _event_data8;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: ((_event_data8 = event.data) === null || _event_data8 === void 0 ? void 0 : _event_data8.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateSpecialistPersonalityMessage)(event.role_id || \"specialist\", \"clarification\"),\n                        timestamp,\n                        type: \"clarification\"\n                    });\n                    break;\n                case \"step_started\":\n                    // Add to typing indicators\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"step_progress\":\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"specialist_message\":\n                    var _event_data9, _event_data10;\n                    const completionMessage = ((_event_data9 = event.data) === null || _event_data9 === void 0 ? void 0 : _event_data9.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateSpecialistPersonalityMessage)(event.role_id || \"specialist\", \"completion\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: \"\".concat(completionMessage, \"\\n\\n\").concat(((_event_data10 = event.data) === null || _event_data10 === void 0 ? void 0 : _event_data10.output) || \"Task completed successfully!\"),\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n                case \"step_completed\":\n                    // Remove from typing\n                    if (event.role_id) {\n                        currentlyTyping.delete(event.role_id);\n                    }\n                    break;\n                case \"handoff_message\":\n                    var _event_data11, _event_data12;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data11 = event.data) === null || _event_data11 === void 0 ? void 0 : _event_data11.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"handoff_message\", (_event_data12 = event.data) === null || _event_data12 === void 0 ? void 0 : _event_data12.fromRole),\n                        timestamp,\n                        type: \"handoff\"\n                    });\n                    break;\n                case \"synthesis_started\":\n                    var _event_data13;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data13 = event.data) === null || _event_data13 === void 0 ? void 0 : _event_data13.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"synthesis_started\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    currentlyTyping.add(\"moderator\");\n                    break;\n                case \"synthesis_complete\":\n                    var _event_data14;\n                    currentlyTyping.delete(\"moderator\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data14 = event.data) === null || _event_data14 === void 0 ? void 0 : _event_data14.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"synthesis_complete\"),\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n            }\n        });\n        setChatMessages(newMessages);\n        setTypingSpecialists(currentlyTyping);\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n            lineNumber: 282,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 text-xs font-medium \".concat(isConnected ? \"bg-green-50 text-green-700 border-b border-green-100\" : \"bg-yellow-50 text-yellow-700 border-b border-yellow-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? \"bg-green-500\" : \"bg-yellow-500\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: isConnected ? \"Connected to AI Team\" : \"Connecting...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    chatMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600 animate-pulse\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Waiting for AI team to start collaboration...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, undefined),\n                    chatMessages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from(typingSpecialists).map((specialist)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_3__.TypingIndicator, {\n                            senderName: specialist,\n                            roleId: specialist !== \"moderator\" ? specialist : undefined\n                        }, specialist, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrchestrationChatroom, \"xNWPH4WkId/aBek//nEKgqbFhaw=\");\n_c = OrchestrationChatroom;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationChatroom\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\n"));

/***/ })

});