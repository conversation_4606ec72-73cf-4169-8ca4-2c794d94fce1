"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"405bdf33e352\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTAxNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQwNWJkZjMzZTM1MlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LayoutContent.tsx":
/*!******************************************!*\
  !*** ./src/components/LayoutContent.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LayoutContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_OptimisticLoadingScreen__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/OptimisticLoadingScreen */ \"(app-pages-browser)/./src/components/OptimisticLoadingScreen.tsx\");\n/* harmony import */ var _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAdvancedPreloading */ \"(app-pages-browser)/./src/hooks/useAdvancedPreloading.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LayoutContent(param) {\n    let { children } = param;\n    _s();\n    const { isCollapsed, collapseSidebar } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const { isNavigating, targetRoute, isPageCached } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__.useNavigation)();\n    // Initialize advanced preloading system\n    (0,_hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_6__.useAdvancedPreloading)({\n        maxConcurrent: 2,\n        idleTimeout: 1500,\n        backgroundDelay: 3000\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-1 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-50 \".concat(isCollapsed ? \"pointer-events-none\" : \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: collapseSidebar,\n                        className: \"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer \".concat(isCollapsed ? \"opacity-0\" : \"opacity-50\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out \".concat(isCollapsed ? \"-translate-x-full\" : \"translate-x-0\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto content-area\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-transition\",\n                                children: isNavigating && targetRoute ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticLoadingScreen__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    targetRoute: targetRoute\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 17\n                                }, this) : children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_s(LayoutContent, \"pw3JeVKm4TvDQ+iCTK8pMVfflCY=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__.useNavigation,\n        _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_6__.useAdvancedPreloading\n    ];\n});\n_c = LayoutContent;\nvar _c;\n$RefreshReg$(_c, \"LayoutContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LayoutContent.tsx\n"));

/***/ })

});