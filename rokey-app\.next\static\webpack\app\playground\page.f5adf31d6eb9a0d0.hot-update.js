"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/app/playground/page.tsx":
/*!*************************************!*\
  !*** ./src/app/playground/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlaygroundPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperClipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilSquareIcon.js\");\n/* harmony import */ var _components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LazyMarkdownRenderer */ \"(app-pages-browser)/./src/components/LazyMarkdownRenderer.tsx\");\n/* harmony import */ var _components_CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* harmony import */ var _components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/RetryDropdown */ \"(app-pages-browser)/./src/components/RetryDropdown.tsx\");\n/* harmony import */ var _components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DynamicStatusIndicator */ \"(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\");\n/* harmony import */ var _components_OrchestrationCanvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/OrchestrationCanvas */ \"(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useMessageStatus */ \"(app-pages-browser)/./src/hooks/useMessageStatus.ts\");\n/* harmony import */ var _utils_performanceLogs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/performanceLogs */ \"(app-pages-browser)/./src/utils/performanceLogs.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Temporarily comment out to fix import issue\n// import { ChatHistorySkeleton, EnhancedChatHistorySkeleton, MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';\n\n\n// import VirtualChatHistory from '@/components/VirtualChatHistory';\n// Import performance logging utilities for browser console access\n\n// Memoized chat history item component to prevent unnecessary re-renders\nconst ChatHistoryItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { chat, currentConversation, onLoadChat, onDeleteChat } = param;\n    const isActive = (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === chat.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 \".concat(isActive ? \"bg-orange-50 border border-orange-200\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onLoadChat(chat),\n                className: \"w-full text-left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 truncate mb-1\",\n                                children: chat.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined),\n                            chat.last_message_preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 line-clamp-2 mb-2\",\n                                children: chat.last_message_preview\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            chat.message_count,\n                                            \" messages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: new Date(chat.updated_at).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    onDeleteChat(chat.id);\n                },\n                className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200\",\n                title: \"Delete conversation\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\n_c = ChatHistoryItem;\nfunction PlaygroundPage() {\n    _s();\n    const { isCollapsed, isHovered, setHoverDisabled } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar)();\n    // Calculate actual sidebar width (collapsed but can expand on hover)\n    const sidebarWidth = !isCollapsed || isHovered ? \"256px\" : \"64px\";\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialPageLoad, setInitialPageLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Prefetch API keys when config is selected for faster retry dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId) {\n            // Prefetch keys in background for retry dropdown\n            fetch(\"/api/keys?custom_config_id=\".concat(selectedConfigId)).then((response)=>response.json()).catch((error)=>console.log(\"Background key prefetch failed:\", error));\n        }\n    }, [\n        selectedConfigId\n    ]);\n    const [messageInput, setMessageInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useStreaming, setUseStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollToBottom, setShowScrollToBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for multiple image handling (up to 10 images)\n    const [imageFiles, setImageFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [imagePreviews, setImagePreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // History sidebar state\n    const [isHistoryCollapsed, setIsHistoryCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Edit message state\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingText, setEditingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoadingMessages, setIsLoadingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Orchestration state\n    const [orchestrationExecutionId, setOrchestrationExecutionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrchestration, setShowOrchestration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Canvas state for split-screen layout\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCanvasMinimized, setIsCanvasMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle canvas state changes\n    const handleCanvasStateChange = (canvasOpen, canvasMinimized)=>{\n        setIsCanvasOpen(canvasOpen);\n        setIsCanvasMinimized(canvasMinimized);\n        // Auto-minimize history sidebar when canvas opens\n        if (canvasOpen && !canvasMinimized) {\n            setIsHistoryCollapsed(true);\n        }\n    };\n    // Disable sidebar hover when canvas is open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setHoverDisabled(isCanvasOpen && !isCanvasMinimized);\n    }, [\n        isCanvasOpen,\n        isCanvasMinimized,\n        setHoverDisabled\n    ]);\n    // Debug logs for orchestration state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDFAD [DEBUG] Orchestration state changed:\", {\n            showOrchestration,\n            orchestrationExecutionId,\n            isCanvasOpen,\n            isCanvasMinimized,\n            shouldRenderCanvas: showOrchestration && orchestrationExecutionId,\n            timestamp: new Date().toISOString()\n        });\n        if (showOrchestration && orchestrationExecutionId) {\n            console.log(\"\\uD83C\\uDFAD [DEBUG] Canvas should be visible now!\");\n        }\n    }, [\n        showOrchestration,\n        orchestrationExecutionId,\n        isCanvasOpen,\n        isCanvasMinimized\n    ]);\n    // Enhanced status tracking\n    const messageStatus = (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus)({\n        enableAutoProgression: true,\n        onStageChange: (stage, timestamp)=>{\n            console.log(\"\\uD83C\\uDFAF Status: \".concat(stage, \" at \").concat(timestamp));\n        }\n    });\n    // Orchestration status tracking\n    const [orchestrationStatus, setOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Function to update orchestration status based on streaming content\n    const updateOrchestrationStatus = (deltaContent, messageStatusObj)=>{\n        let newStatus = \"\";\n        if (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\")) {\n            newStatus = \"Multi-Role AI Orchestration Started\";\n        } else if (deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\")) {\n            newStatus = \"Planning specialist assignments\";\n        } else if (deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\")) {\n            newStatus = \"Moderator coordinating specialists\";\n        } else if (deltaContent.includes(\"Specialist:\") && deltaContent.includes(\"Working...\")) {\n            // Extract specialist name\n            const specialistMatch = deltaContent.match(/(\\w+)\\s+Specialist:/);\n            if (specialistMatch) {\n                newStatus = \"\".concat(specialistMatch[1], \" Specialist working\");\n            } else {\n                newStatus = \"Specialist working on your request\";\n            }\n        } else if (deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\")) {\n            newStatus = \"Synthesizing specialist responses\";\n        } else if (deltaContent.includes(\"Analyzing and processing\")) {\n            newStatus = \"Analyzing and processing with specialized expertise\";\n        }\n        if (newStatus && newStatus !== orchestrationStatus) {\n            console.log(\"\\uD83C\\uDFAD Orchestration status update:\", newStatus);\n            setOrchestrationStatus(newStatus);\n            messageStatusObj.updateOrchestrationStatus(newStatus);\n        }\n    };\n    // Auto-continuation function for seamless multi-part responses\n    const handleAutoContinuation = async ()=>{\n        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Starting automatic continuation...\");\n        if (!selectedConfigId || !currentConversation) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Missing config or conversation\");\n            return;\n        }\n        setIsLoading(true);\n        setOrchestrationStatus(\"Continuing synthesis automatically...\");\n        messageStatus.startProcessing();\n        try {\n            // Create a continuation message\n            const continuationMessage = {\n                id: Date.now().toString() + \"-continue\",\n                role: \"user\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"continue\"\n                    }\n                ]\n            };\n            // Add the continuation message to the UI\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    continuationMessage\n                ]);\n            // Save continuation message to database\n            await saveMessageToDatabase(currentConversation.id, continuationMessage);\n            // Prepare payload for continuation\n            const continuationPayload = {\n                custom_api_config_id: selectedConfigId,\n                messages: [\n                    ...messages.map((m)=>({\n                            role: m.role,\n                            content: m.content.length === 1 && m.content[0].type === \"text\" ? m.content[0].text : m.content\n                        })),\n                    {\n                        role: \"user\",\n                        content: \"continue\"\n                    }\n                ],\n                stream: useStreaming\n            };\n            // Make the continuation request\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(continuationPayload),\n                cache: \"no-store\"\n            });\n            // Check for synthesis completion response\n            if (response.ok) {\n                // Check if this is a synthesis completion response\n                const responseText = await response.text();\n                let responseData;\n                try {\n                    responseData = JSON.parse(responseText);\n                } catch (e) {\n                    // If it's not JSON, treat as regular response\n                    responseData = null;\n                }\n                // Handle synthesis completion\n                if ((responseData === null || responseData === void 0 ? void 0 : responseData.error) === \"synthesis_complete\") {\n                    console.log('\\uD83C\\uDF89 [AUTO-CONTINUE] Synthesis is complete! Treating \"continue\" as new conversation.');\n                    // Remove the continuation message we just added\n                    setMessages((prevMessages)=>prevMessages.slice(0, -1));\n                    // Clear the loading state\n                    setIsLoading(false);\n                    setOrchestrationStatus(\"\");\n                    messageStatus.markComplete();\n                    // Process the \"continue\" as a new message by calling the normal send flow\n                    // But first we need to set the input back to \"continue\"\n                    setMessageInput(\"continue\");\n                    // Call the normal send message flow which will handle it as a new conversation\n                    setTimeout(()=>{\n                        handleSendMessage();\n                    }, 100);\n                    return;\n                }\n                // If not synthesis completion, recreate the response for normal processing\n                const recreatedResponse = new Response(responseText, {\n                    status: response.status,\n                    statusText: response.statusText,\n                    headers: response.headers\n                });\n                // Handle the continuation response\n                if (useStreaming && recreatedResponse.body) {\n                    const reader = recreatedResponse.body.getReader();\n                    const decoder = new TextDecoder();\n                    let assistantMessageId = Date.now().toString() + \"-assistant-continue\";\n                    let currentAssistantMessage = {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: [\n                            {\n                                type: \"text\",\n                                text: \"\"\n                            }\n                        ]\n                    };\n                    setMessages((prevMessages)=>[\n                            ...prevMessages,\n                            currentAssistantMessage\n                        ]);\n                    let accumulatedText = \"\";\n                    let isOrchestrationDetected = false;\n                    let streamingStatusTimeout = null;\n                    // Check response headers to determine if this is chunked synthesis continuation\n                    const synthesisProgress = recreatedResponse.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = recreatedResponse.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    if (isChunkedSynthesis) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation\");\n                        messageStatus.markStreaming();\n                        setOrchestrationStatus(\"\");\n                    } else {\n                        // Start with continuation status, but allow orchestration detection to override\n                        messageStatus.markOrchestrationStarted();\n                        setOrchestrationStatus(\"Continuing synthesis...\");\n                        // Set up delayed streaming status, but allow orchestration detection to override\n                        streamingStatusTimeout = setTimeout(()=>{\n                            if (!isOrchestrationDetected) {\n                                console.log(\"\\uD83C\\uDFAF [AUTO-CONTINUE] No orchestration detected - switching to typing status\");\n                                messageStatus.markStreaming();\n                                setOrchestrationStatus(\"\");\n                            }\n                        }, 800);\n                    }\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const jsonData = line.substring(6);\n                                if (jsonData.trim() === \"[DONE]\") break;\n                                try {\n                                    var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                    const parsedChunk = JSON.parse(jsonData);\n                                    if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                        const deltaContent = parsedChunk.choices[0].delta.content;\n                                        accumulatedText += deltaContent;\n                                        // Only check for orchestration if this is NOT a chunked synthesis continuation\n                                        if (!isChunkedSynthesis && !isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                            console.log(\"\\uD83C\\uDFAD [AUTO-CONTINUE] Detected NEW orchestration - this should be direct continuation instead\");\n                                            isOrchestrationDetected = true;\n                                            // Cancel the delayed streaming status\n                                            if (streamingStatusTimeout) {\n                                                clearTimeout(streamingStatusTimeout);\n                                                streamingStatusTimeout = null;\n                                            }\n                                            // Update orchestration status for new orchestration\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else if (!isChunkedSynthesis && isOrchestrationDetected) {\n                                            // Continue updating orchestration status if already detected\n                                            updateOrchestrationStatus(deltaContent, messageStatus);\n                                        } else {\n                                        // This is direct continuation content (chunked synthesis or regular continuation)\n                                        // Keep the current status without changing it\n                                        }\n                                        const textContent = currentAssistantMessage.content[0];\n                                        textContent.text = accumulatedText;\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: [\n                                                        textContent\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Auto-continuation: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                                }\n                            }\n                        }\n                    }\n                    // Clean up timeout if still pending\n                    if (streamingStatusTimeout) {\n                        clearTimeout(streamingStatusTimeout);\n                    }\n                    // Save the continuation response\n                    if (accumulatedText) {\n                        const finalContinuationMessage = {\n                            ...currentAssistantMessage,\n                            content: [\n                                {\n                                    type: \"text\",\n                                    text: accumulatedText\n                                }\n                            ]\n                        };\n                        // Check if we need auto-continuation for chunked synthesis\n                        const needsAutoContinuation = isChunkedSynthesis && synthesisComplete !== \"true\" && accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\");\n                        if (needsAutoContinuation) {\n                            console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation, starting next chunk...\");\n                            // Save current message first\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                            // Start auto-continuation after a brief delay\n                            setTimeout(()=>{\n                                handleAutoContinuation();\n                            }, 1000);\n                        } else {\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                        }\n                    }\n                }\n            } else {\n                // Handle non-ok response\n                throw new Error(\"Auto-continuation failed: \".concat(response.status));\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Error:\", error);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-continue\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"Auto-continuation failed: \".concat(error instanceof Error ? error.message : \"Unknown error\")\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n            setOrchestrationStatus(\"\");\n            messageStatus.markComplete();\n        }\n    };\n    // Enhanced chat history with optimized caching\n    const { chatHistory, isLoading: isLoadingHistory, isStale: isChatHistoryStale, error: chatHistoryError, refetch: refetchChatHistory, prefetch: prefetchChatHistory, invalidateCache: invalidateChatHistoryCache } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory)({\n        configId: selectedConfigId,\n        enablePrefetch: true,\n        cacheTimeout: 300000,\n        staleTimeout: 30000 // 30 seconds - show stale data while fetching fresh\n    });\n    // Chat history prefetching hook\n    const { prefetchChatHistory: prefetchForNavigation } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    // Conversation starters\n    const conversationStarters = [\n        {\n            id: \"write-copy\",\n            title: \"Write copy\",\n            description: \"Create compelling marketing content\",\n            icon: \"✍️\",\n            color: \"bg-amber-100 text-amber-700\",\n            prompt: \"Help me write compelling copy for my product landing page\"\n        },\n        {\n            id: \"image-generation\",\n            title: \"Image generation\",\n            description: \"Create visual content descriptions\",\n            icon: \"\\uD83C\\uDFA8\",\n            color: \"bg-blue-100 text-blue-700\",\n            prompt: \"Help me create detailed prompts for AI image generation\"\n        },\n        {\n            id: \"create-avatar\",\n            title: \"Create avatar\",\n            description: \"Design character personas\",\n            icon: \"\\uD83D\\uDC64\",\n            color: \"bg-green-100 text-green-700\",\n            prompt: \"Help me create a detailed character avatar for my story\"\n        },\n        {\n            id: \"write-code\",\n            title: \"Write code\",\n            description: \"Generate and debug code\",\n            icon: \"\\uD83D\\uDCBB\",\n            color: \"bg-purple-100 text-purple-700\",\n            prompt: \"Help me write clean, efficient code for my project\"\n        }\n    ];\n    // Fetch Custom API Configs for the dropdown with progressive loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchConfigs = async ()=>{\n            try {\n                // Progressive loading: render UI first, then load configs\n                if (initialPageLoad) {\n                    await new Promise((resolve)=>setTimeout(resolve, 50));\n                }\n                const response = await fetch(\"/api/custom-configs\");\n                if (!response.ok) {\n                    const errData = await response.json();\n                    throw new Error(errData.error || \"Failed to fetch configurations\");\n                }\n                const data = await response.json();\n                setCustomConfigs(data);\n                if (data.length > 0) {\n                    setSelectedConfigId(data[0].id);\n                }\n                setInitialPageLoad(false);\n            } catch (err) {\n                setError(\"Failed to load configurations: \".concat(err.message));\n                setCustomConfigs([]);\n                setInitialPageLoad(false);\n            }\n        };\n        // Call immediately to ensure configs load properly\n        fetchConfigs();\n    }, [\n        initialPageLoad\n    ]);\n    // Helper function to convert File to base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    const handleImageChange = async (event)=>{\n        const files = Array.from(event.target.files || []);\n        if (files.length === 0) return;\n        // Limit to 10 images total\n        const currentCount = imageFiles.length;\n        const availableSlots = 10 - currentCount;\n        const filesToAdd = files.slice(0, availableSlots);\n        if (filesToAdd.length < files.length) {\n            setError(\"You can only upload up to 10 images. \".concat(files.length - filesToAdd.length, \" images were not added.\"));\n        }\n        try {\n            const newPreviews = [];\n            for (const file of filesToAdd){\n                const previewUrl = await fileToBase64(file);\n                newPreviews.push(previewUrl);\n            }\n            setImageFiles((prev)=>[\n                    ...prev,\n                    ...filesToAdd\n                ]);\n            setImagePreviews((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error processing images:\", error);\n            setError(\"Failed to process one or more images. Please try again.\");\n        }\n        // Reset file input\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleRemoveImage = (index)=>{\n        if (index !== undefined) {\n            // Remove specific image\n            setImageFiles((prev)=>prev.filter((_, i)=>i !== index));\n            setImagePreviews((prev)=>prev.filter((_, i)=>i !== index));\n        } else {\n            // Remove all images\n            setImageFiles([]);\n            setImagePreviews([]);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\"; // Reset file input\n        }\n    };\n    // Scroll management functions\n    const scrollToBottom = function() {\n        let smooth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (messagesContainerRef.current) {\n            messagesContainerRef.current.scrollTo({\n                top: messagesContainerRef.current.scrollHeight,\n                behavior: smooth ? \"smooth\" : \"auto\"\n            });\n        }\n    };\n    const handleScroll = (e)=>{\n        const container = e.currentTarget;\n        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n        setShowScrollToBottom(!isNearBottom && messages.length > 0);\n    };\n    // Auto-scroll to bottom when new messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messages.length > 0) {\n            // Use requestAnimationFrame to ensure DOM has updated\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages.length\n    ]);\n    // Auto-scroll during streaming responses\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            // Scroll to bottom during streaming to show new content\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Auto-scroll when streaming content updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            const lastMessage = messages[messages.length - 1];\n            if (lastMessage && lastMessage.role === \"assistant\") {\n                // Scroll to bottom when assistant message content updates during streaming\n                requestAnimationFrame(()=>{\n                    scrollToBottom();\n                });\n            }\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Handle sidebar state changes to ensure proper centering\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Small delay to allow CSS transitions to complete\n        const timer = setTimeout(()=>{\n            if (messages.length > 0) {\n                // Maintain scroll position when sidebar toggles\n                requestAnimationFrame(()=>{\n                    if (messagesContainerRef.current) {\n                        const container = messagesContainerRef.current;\n                        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n                        if (isNearBottom) {\n                            scrollToBottom();\n                        }\n                    }\n                });\n            }\n        }, 200); // Match the transition duration\n        return ()=>clearTimeout(timer);\n    }, [\n        isCollapsed,\n        isHovered,\n        isHistoryCollapsed,\n        messages.length\n    ]);\n    // Prefetch chat history when hovering over configs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId && customConfigs.length > 0) {\n            // Prefetch chat history for other configs when user is idle\n            const otherConfigs = customConfigs.filter((config)=>config.id !== selectedConfigId).slice(0, 3); // Limit to 3 most recent other configs\n            const timer = setTimeout(()=>{\n                otherConfigs.forEach((config)=>{\n                    prefetchForNavigation(config.id);\n                });\n            }, 2000); // Wait 2 seconds before prefetching\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        selectedConfigId,\n        customConfigs,\n        prefetchForNavigation\n    ]);\n    // Load messages for a specific conversation with pagination\n    const loadConversation = async function(conversation) {\n        let loadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Set loading state for message loading\n        if (!loadMore) {\n            setIsLoadingMessages(true);\n        }\n        try {\n            // Note: isLoadingHistory is now managed by the useChatHistory hook\n            // For initial load, get latest 50 messages\n            // For load more, get older messages with offset\n            const limit = 50;\n            const offset = loadMore ? messages.length : 0;\n            const latest = !loadMore;\n            // Add cache-busting parameter to ensure fresh data after edits\n            const cacheBuster = Date.now();\n            const response = await fetch(\"/api/chat/messages?conversation_id=\".concat(conversation.id, \"&limit=\").concat(limit, \"&offset=\").concat(offset, \"&latest=\").concat(latest, \"&_cb=\").concat(cacheBuster), {\n                cache: \"no-store\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load conversation messages\");\n            }\n            const chatMessages = await response.json();\n            // Convert ChatMessage to PlaygroundMessage format\n            const playgroundMessages = chatMessages.map((msg)=>({\n                    id: msg.id,\n                    role: msg.role,\n                    content: msg.content.map((part)=>{\n                        var _part_image_url;\n                        if (part.type === \"text\" && part.text) {\n                            return {\n                                type: \"text\",\n                                text: part.text\n                            };\n                        } else if (part.type === \"image_url\" && ((_part_image_url = part.image_url) === null || _part_image_url === void 0 ? void 0 : _part_image_url.url)) {\n                            return {\n                                type: \"image_url\",\n                                image_url: {\n                                    url: part.image_url.url\n                                }\n                            };\n                        } else {\n                            // Fallback for malformed content\n                            return {\n                                type: \"text\",\n                                text: \"\"\n                            };\n                        }\n                    })\n                }));\n            if (loadMore) {\n                // Prepend older messages to the beginning\n                setMessages((prev)=>[\n                        ...playgroundMessages,\n                        ...prev\n                    ]);\n            } else {\n                // Replace all messages for initial load\n                setMessages(playgroundMessages);\n                // Note: currentConversation is now set optimistically in loadChatFromHistory\n                // Only set it here if it's not already set (for direct loadConversation calls)\n                if (!currentConversation || currentConversation.id !== conversation.id) {\n                    setCurrentConversation(conversation);\n                }\n            }\n            setError(null);\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        } finally{\n            // Clear loading state for message loading\n            if (!loadMore) {\n                setIsLoadingMessages(false);\n            }\n        // Note: isLoadingHistory is now managed by the useChatHistory hook\n        }\n    };\n    // Save current conversation\n    const saveConversation = async ()=>{\n        if (!selectedConfigId || messages.length === 0) return null;\n        try {\n            let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const firstMessage = messages[0];\n                let title = \"New Chat\";\n                if (firstMessage && firstMessage.content.length > 0) {\n                    const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                    if (textPart && textPart.text) {\n                        title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                    }\n                }\n                const newConversationData = {\n                    custom_api_config_id: selectedConfigId,\n                    title\n                };\n                const response = await fetch(\"/api/chat/conversations\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newConversationData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation\");\n                }\n                const newConversation = await response.json();\n                conversationId = newConversation.id;\n                setCurrentConversation(newConversation);\n            }\n            // Save all messages that aren't already saved\n            for (const message of messages){\n                // Check if message is already saved (has UUID format)\n                if (message.id.includes(\"-\") && message.id.length > 20) continue;\n                const newMessageData = {\n                    conversation_id: conversationId,\n                    role: message.role,\n                    content: message.content\n                };\n                await fetch(\"/api/chat/messages\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newMessageData)\n                });\n            }\n            // Only refresh chat history if we created a new conversation\n            if (!currentConversation) {\n                refetchChatHistory(true); // Force refresh for new conversations\n            }\n            return conversationId;\n        } catch (err) {\n            console.error(\"Error saving conversation:\", err);\n            setError(\"Failed to save conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Delete a conversation\n    const deleteConversation = async (conversationId)=>{\n        try {\n            const response = await fetch(\"/api/chat/conversations?id=\".concat(conversationId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete conversation\");\n            }\n            // If this was the current conversation, clear it\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n            // Force refresh chat history after deletion\n            refetchChatHistory(true);\n        } catch (err) {\n            console.error(\"Error deleting conversation:\", err);\n            setError(\"Failed to delete conversation: \".concat(err.message));\n        }\n    };\n    // Create a new conversation automatically when first message is sent\n    const createNewConversation = async (firstMessage)=>{\n        if (!selectedConfigId) return null;\n        try {\n            // Generate title from first message\n            let title = \"New Chat\";\n            if (firstMessage.content.length > 0) {\n                const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                if (textPart && textPart.text) {\n                    title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                }\n            }\n            const newConversationData = {\n                custom_api_config_id: selectedConfigId,\n                title\n            };\n            const response = await fetch(\"/api/chat/conversations\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newConversationData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to create conversation\");\n            }\n            const newConversation = await response.json();\n            setCurrentConversation(newConversation);\n            return newConversation.id;\n        } catch (err) {\n            console.error(\"Error creating conversation:\", err);\n            setError(\"Failed to create conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Save individual message to database\n    const saveMessageToDatabase = async (conversationId, message)=>{\n        try {\n            const newMessageData = {\n                conversation_id: conversationId,\n                role: message.role,\n                content: message.content\n            };\n            const response = await fetch(\"/api/chat/messages\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newMessageData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save message\");\n            }\n            return await response.json();\n        } catch (err) {\n            console.error(\"Error saving message:\", err);\n        // Don't show error to user for message saving failures\n        // The conversation will still work in the UI\n        }\n    };\n    const handleStarterClick = (prompt)=>{\n        setMessageInput(prompt);\n        // Auto-focus the input after setting the prompt\n        setTimeout(()=>{\n            const textarea = document.querySelector('textarea[placeholder*=\"Type a message\"]');\n            if (textarea) {\n                textarea.focus();\n                textarea.setSelectionRange(textarea.value.length, textarea.value.length);\n            }\n        }, 100);\n    };\n    const startNewChat = async ()=>{\n        // Save current conversation if it has messages\n        if (messages.length > 0) {\n            await saveConversation();\n        }\n        setMessages([]);\n        setCurrentConversation(null);\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Reset status tracking\n        messageStatus.reset();\n    };\n    // Handle model/router configuration change\n    const handleConfigChange = async (newConfigId)=>{\n        // Don't do anything if it's the same config\n        if (newConfigId === selectedConfigId) return;\n        // If there's an existing conversation with messages, start a new chat\n        if (messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [Model Switch] Starting new chat due to model change\");\n            await startNewChat();\n        }\n        // Update the selected configuration\n        setSelectedConfigId(newConfigId);\n        // Find the config name for logging\n        const selectedConfig = customConfigs.find((config)=>config.id === newConfigId);\n        const configName = selectedConfig ? selectedConfig.name : newConfigId;\n        console.log(\"\\uD83D\\uDD04 [Model Switch] Switched to config: \".concat(configName, \" (\").concat(newConfigId, \")\"));\n    };\n    const loadChatFromHistory = async (conversation)=>{\n        // Optimistic UI update - immediately switch to the selected conversation\n        console.log(\"\\uD83D\\uDD04 [INSTANT SWITCH] Immediately switching to conversation: \".concat(conversation.title));\n        // Clear current state immediately for instant feedback\n        setCurrentConversation(conversation);\n        setMessages([]); // Clear messages immediately to show loading state\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Save current conversation in background (non-blocking)\n        const savePromise = (async ()=>{\n            if (messages.length > 0 && !currentConversation) {\n                try {\n                    await saveConversation();\n                } catch (err) {\n                    console.error(\"Background save failed:\", err);\n                }\n            }\n        })();\n        // Load conversation messages in background\n        try {\n            await loadConversation(conversation);\n            console.log(\"✅ [INSTANT SWITCH] Successfully loaded conversation: \".concat(conversation.title));\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        // Don't revert currentConversation - keep the UI showing the selected conversation\n        }\n        // Ensure background save completes\n        await savePromise;\n    };\n    // Edit message functionality\n    const startEditingMessage = (messageId, currentText)=>{\n        setEditingMessageId(messageId);\n        setEditingText(currentText);\n    };\n    const cancelEditingMessage = ()=>{\n        setEditingMessageId(null);\n        setEditingText(\"\");\n    };\n    const saveEditedMessage = async ()=>{\n        if (!editingMessageId || !editingText.trim() || !selectedConfigId) return;\n        // Find the index of the message being edited\n        const messageIndex = messages.findIndex((msg)=>msg.id === editingMessageId);\n        if (messageIndex === -1) return;\n        // Update the message content\n        const updatedMessages = [\n            ...messages\n        ];\n        updatedMessages[messageIndex] = {\n            ...updatedMessages[messageIndex],\n            content: [\n                {\n                    type: \"text\",\n                    text: editingText.trim()\n                }\n            ]\n        };\n        // Remove all messages after the edited message (restart conversation from this point)\n        const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);\n        setMessages(messagesToKeep);\n        setEditingMessageId(null);\n        setEditingText(\"\");\n        // If we have a current conversation, update the database\n        if (currentConversation) {\n            try {\n                // Delete messages after the edited one from the database\n                const messagesToDelete = messages.slice(messageIndex + 1);\n                console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting \".concat(messagesToDelete.length, \" messages after edited message\"));\n                // Instead of trying to identify saved messages by ID format,\n                // delete all messages after the edited message's timestamp from the database\n                if (messagesToDelete.length > 0) {\n                    const editedMessage = messages[messageIndex];\n                    const editedMessageTimestamp = parseInt(editedMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting all messages after timestamp: \".concat(editedMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            after_timestamp: editedMessageTimestamp\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages after timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [EDIT MODE] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Update/save the edited message in the database\n                const editedMessage = messagesToKeep[messageIndex];\n                console.log(\"✏️ [EDIT MODE] Saving edited message with timestamp: \".concat(editedMessage.id));\n                // Use timestamp-based update to find and update the message\n                const updateResponse = await fetch(\"/api/chat/messages/update-by-timestamp\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        conversation_id: currentConversation.id,\n                        timestamp: parseInt(editedMessage.id),\n                        content: editedMessage.content\n                    })\n                });\n                if (!updateResponse.ok) {\n                    console.error(\"Failed to update message by timestamp:\", await updateResponse.text());\n                    // If update fails, try to save as new message (fallback)\n                    console.log(\"\\uD83D\\uDCDD [EDIT MODE] Fallback: Saving edited message as new message\");\n                    await saveMessageToDatabase(currentConversation.id, editedMessage);\n                } else {\n                    const result = await updateResponse.json();\n                    console.log(\"✅ [EDIT MODE] Successfully updated message: \".concat(result.message));\n                }\n                // Force refresh chat history to reflect changes and clear cache\n                refetchChatHistory(true);\n                // Also clear any message cache by adding a cache-busting parameter\n                if (true) {\n                    // Clear any cached conversation data\n                    const cacheKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"chat_\") || key.startsWith(\"conversation_\"));\n                    cacheKeys.forEach((key)=>localStorage.removeItem(key));\n                }\n            } catch (err) {\n                console.error(\"Error updating conversation:\", err);\n                setError(\"Failed to update conversation: \".concat(err.message));\n            }\n        }\n        // Now automatically send the edited message to get a response\n        await sendEditedMessageToAPI(messagesToKeep);\n    };\n    // Send the edited conversation to get a new response\n    const sendEditedMessageToAPI = async (conversationMessages)=>{\n        if (!selectedConfigId || conversationMessages.length === 0) return;\n        setIsLoading(true);\n        setError(null);\n        // Start status tracking for edit mode\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Sending edited conversation for new response...\");\n        // Prepare payload with the conversation up to the edited message\n        const messagesForPayload = conversationMessages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming\n        };\n        try {\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [EDIT MODE] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            if (useStreaming && response.body) {\n                // Handle streaming response with orchestration detection (same as handleSendMessage)\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [EDIT MODE] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Failed to parse stream chunk:\", parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                // Save the assistant response with auto-continuation support\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                        // Start auto-continuation after a brief delay\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, 2000);\n                    } else {\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                    }\n                }\n            } else {\n                var _data_choices__message, _data_choices_, _data_choices, _data_content_, _data_content;\n                // Handle non-streaming response\n                const data = await response.json();\n                let assistantContent = \"Could not parse assistant's response.\";\n                if ((_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content) {\n                    assistantContent = data.choices[0].message.content;\n                } else if ((_data_content = data.content) === null || _data_content === void 0 ? void 0 : (_data_content_ = _data_content[0]) === null || _data_content_ === void 0 ? void 0 : _data_content_.text) {\n                    assistantContent = data.content[0].text;\n                } else if (typeof data.text === \"string\") {\n                    assistantContent = data.text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save the assistant response\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Edit mode API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [EDIT MODE] Processing complete\");\n        }\n    };\n    // Handle retry message with optional specific API key\n    const handleRetryMessage = async (messageIndex, apiKeyId)=>{\n        if (!selectedConfigId || messageIndex < 0 || messageIndex >= messages.length) return;\n        const messageToRetry = messages[messageIndex];\n        if (messageToRetry.role !== \"assistant\") return;\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start status tracking for retry\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [RETRY] Retrying message with\", apiKeyId ? \"specific key: \".concat(apiKeyId) : \"same model\");\n        // Remove the assistant message and any messages after it\n        const messagesToKeep = messages.slice(0, messageIndex);\n        setMessages(messagesToKeep);\n        // If we have a current conversation, delete the retried message and subsequent ones from database\n        if (currentConversation) {\n            try {\n                const messagesToDelete = messages.slice(messageIndex);\n                console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting \".concat(messagesToDelete.length, \" messages from retry point\"));\n                // Delete all messages from the retry point onwards using timestamp-based deletion\n                if (messagesToDelete.length > 0) {\n                    const retryMessage = messages[messageIndex];\n                    const retryMessageTimestamp = parseInt(retryMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting all messages from timestamp: \".concat(retryMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            from_timestamp: retryMessageTimestamp // Use 'from' instead of 'after' to include the retry message\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages from timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [RETRY] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Refresh chat history to reflect changes\n                refetchChatHistory(true);\n            } catch (err) {\n                console.error(\"Error deleting retried messages:\", err);\n            }\n        }\n        // Prepare payload with messages up to the retry point\n        const messagesForPayload = messagesToKeep.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming,\n            ...apiKeyId && {\n                specific_api_key_id: apiKeyId\n            } // Add specific key if provided\n        };\n        try {\n            console.log(\"\\uD83D\\uDE80 [RETRY] Starting retry API call...\");\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [RETRY] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Check for orchestration headers in retry\n            const orchestrationId = response.headers.get(\"X-RoKey-Orchestration-ID\");\n            const orchestrationActive = response.headers.get(\"X-RoKey-Orchestration-Active\");\n            if (orchestrationId && orchestrationActive === \"true\") {\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration headers in retry - showing canvas\");\n                setOrchestrationExecutionId(orchestrationId);\n                setShowOrchestration(true);\n            }\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [RETRY] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            // Handle streaming or non-streaming response (reuse existing logic)\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let accumulatedText = \"\";\n                const currentAssistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const data = line.slice(6);\n                                if (data === \"[DONE]\") continue;\n                                try {\n                                    var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                    const parsed = JSON.parse(data);\n                                    if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                        const newContent = parsed.choices[0].delta.content;\n                                        accumulatedText += newContent;\n                                        // Detect orchestration content and update status dynamically\n                                        if (newContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || newContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || newContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || newContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || newContent.includes(\"Specialist:\")) {\n                                            console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                            messageStatus.markOrchestrationStarted();\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        } else if (orchestrationStatus) {\n                                            // Continue updating orchestration status if already in orchestration mode\n                                            updateOrchestrationStatus(newContent, messageStatus);\n                                        }\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === currentAssistantMessage.id ? {\n                                                    ...msg,\n                                                    content: [\n                                                        {\n                                                            type: \"text\",\n                                                            text: accumulatedText\n                                                        }\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Failed to parse streaming chunk:\", parseError);\n                                }\n                            }\n                        }\n                    }\n                } finally{\n                    reader.releaseLock();\n                }\n                // Save final assistant message\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                }\n            } else {\n                // Non-streaming response\n                const data = await response.json();\n                let assistantContent = \"\";\n                if (data.choices && data.choices.length > 0 && data.choices[0].message) {\n                    assistantContent = data.choices[0].message.content;\n                } else if (data.content && Array.isArray(data.content) && data.content.length > 0) {\n                    assistantContent = data.content[0].text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save assistant message\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Retry API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-retry\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred during retry.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Save error message\n            if (currentConversation) {\n                await saveMessageToDatabase(currentConversation.id, errorMessage);\n            }\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [RETRY] Processing complete\");\n        }\n    };\n    const handleSendMessage = async (e)=>{\n        if (e) e.preventDefault();\n        // Allow sending if there's text OR images\n        if (!messageInput.trim() && imageFiles.length === 0 || !selectedConfigId) return;\n        // Check if this is a continuation request\n        const inputText = messageInput.trim().toLowerCase();\n        if (inputText === \"continue\" && messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [CONTINUE] Detected manual continuation request, routing to auto-continuation...\");\n            // Clear the input\n            setMessageInput(\"\");\n            // Route to auto-continuation instead of normal message flow\n            await handleAutoContinuation();\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start enhanced status tracking\n        messageStatus.startProcessing();\n        // Phase 1 Optimization: Performance tracking\n        const messagingStartTime = performance.now();\n        console.log(\"\\uD83D\\uDE80 [MESSAGING FLOW] Starting optimized parallel processing...\");\n        // Capture current input and images before clearing them\n        const currentMessageInput = messageInput.trim();\n        const currentImageFiles = [\n            ...imageFiles\n        ];\n        const currentImagePreviews = [\n            ...imagePreviews\n        ];\n        // Clear input and images immediately to prevent them from showing after send\n        setMessageInput(\"\");\n        handleRemoveImage();\n        const userMessageContentParts = [];\n        let apiMessageContentParts = []; // For the API payload, image_url.url will be base64\n        if (currentMessageInput) {\n            userMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n            apiMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n        }\n        // Process all images\n        if (currentImageFiles.length > 0) {\n            try {\n                for(let i = 0; i < currentImageFiles.length; i++){\n                    const file = currentImageFiles[i];\n                    const preview = currentImagePreviews[i];\n                    const base64ImageData = await fileToBase64(file);\n                    // For UI display (uses the preview which is already base64)\n                    userMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: preview\n                        }\n                    });\n                    // For API payload\n                    apiMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: base64ImageData\n                        }\n                    });\n                }\n            } catch (imgErr) {\n                console.error(\"Error converting images to base64:\", imgErr);\n                setError(\"Failed to process one or more images. Please try again.\");\n                setIsLoading(false);\n                // Restore the input and images if there was an error\n                setMessageInput(currentMessageInput);\n                setImageFiles(currentImageFiles);\n                setImagePreviews(currentImagePreviews);\n                return;\n            }\n        }\n        const newUserMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: userMessageContentParts\n        };\n        setMessages((prevMessages)=>[\n                ...prevMessages,\n                newUserMessage\n            ]);\n        // Phase 1 Optimization: Start conversation creation and user message saving in background\n        // Don't wait for these operations - they can happen in parallel with LLM call\n        let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        let conversationPromise = Promise.resolve(conversationId);\n        let userMessageSavePromise = Promise.resolve();\n        if (!conversationId && !currentConversation) {\n            console.log(\"\\uD83D\\uDD04 [PARALLEL] Starting conversation creation in background...\");\n            conversationPromise = createNewConversation(newUserMessage);\n        }\n        // Start user message saving in background (will wait for conversation if needed)\n        userMessageSavePromise = conversationPromise.then(async (convId)=>{\n            if (convId) {\n                console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving user message in background...\");\n                await saveMessageToDatabase(convId, newUserMessage);\n                console.log(\"✅ [PARALLEL] User message saved\");\n                return convId;\n            }\n        }).catch((err)=>{\n            console.error(\"❌ [PARALLEL] User message save failed:\", err);\n        });\n        // Prepare payload.messages by transforming existing messages and adding the new one\n        const existingMessagesForPayload = messages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                // System messages are always simple text strings\n                // Their content in PlaygroundMessage is [{type: 'text', text: 'Actual system prompt'}]\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\"; // Fallback, though system messages should always be text\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                // Single text part for user/assistant, send as string for API\n                contentForApi = m.content[0].text;\n            } else {\n                // Multimodal content (e.g., user message with image) or multiple parts\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        // The part.image_url.url from messages state is the base64 data URL (preview)\n                        // This is what we want to send to the backend.\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    // Ensure it's properly cast for text part before accessing .text\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: [\n                ...existingMessagesForPayload,\n                {\n                    role: \"user\",\n                    content: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === \"text\" ? apiMessageContentParts[0].text : apiMessageContentParts\n                }\n            ],\n            stream: useStreaming\n        };\n        try {\n            // Phase 1 Optimization: Start LLM call immediately in parallel with background operations\n            console.log(\"\\uD83D\\uDE80 [PARALLEL] Starting LLM API call...\");\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                // Conservative performance optimizations\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [PARALLEL] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Check for orchestration headers\n            const orchestrationId = response.headers.get(\"X-RoKey-Orchestration-ID\");\n            const orchestrationActive = response.headers.get(\"X-RoKey-Orchestration-Active\");\n            console.log(\"\\uD83C\\uDFAD [DEBUG] Checking orchestration headers:\", {\n                orchestrationId,\n                orchestrationActive,\n                allHeaders: Object.fromEntries(response.headers.entries())\n            });\n            if (orchestrationId && orchestrationActive === \"true\") {\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration headers - showing canvas\");\n                setOrchestrationExecutionId(orchestrationId);\n                setShowOrchestration(true);\n            } else {\n                console.log(\"\\uD83C\\uDFAD [DEBUG] No orchestration headers found or not active\");\n            }\n            // If we're here, it's a stream.\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Detect orchestration content and update status dynamically\n                                    if (!isOrchestrationDetected && (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\") || deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\") || deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\") || deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\") || deltaContent.includes(\"Specialist:\"))) {\n                                        console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration theater content - switching to orchestration status\");\n                                        isOrchestrationDetected = true;\n                                        // Cancel the delayed streaming status\n                                        if (streamingStatusTimeout) {\n                                            clearTimeout(streamingStatusTimeout);\n                                            streamingStatusTimeout = null;\n                                        }\n                                        // Switch to orchestration status instead of marking complete\n                                        messageStatus.markOrchestrationStarted();\n                                    }\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Playground: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                if (accumulatedText) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check response headers to determine if this is chunked synthesis\n                    const synthesisProgress = response.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = response.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                        // For chunked synthesis, start continuation immediately\n                        // For regular synthesis, add a delay\n                        const delay = isChunkedSynthesis ? 1000 : 2000;\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, delay);\n                    } else {\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                    }\n                }\n            }\n        } catch (err) {\n            console.error(\"Playground API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Phase 1 Optimization: Save error message in background\n            conversationPromise.then(async (convId)=>{\n                if (convId) {\n                    console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving error message in background...\");\n                    await saveMessageToDatabase(convId, errorMessage);\n                    console.log(\"✅ [PARALLEL] Error message saved\");\n                }\n            }).catch((saveErr)=>{\n                console.error(\"❌ [PARALLEL] Error message save failed:\", saveErr);\n            });\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.logStatusPerformance)(messageStatus.stageHistory);\n            // Phase 1 Optimization: Performance summary\n            const totalMessagingTime = performance.now() - messagingStartTime;\n            console.log(\"\\uD83D\\uDCCA [MESSAGING FLOW] Total time: \".concat(totalMessagingTime.toFixed(1), \"ms\"));\n            // Phase 1 Optimization: Refresh chat history in background, don't block UI\n            conversationPromise.then(async (convId)=>{\n                if (convId && !currentConversation) {\n                    console.log(\"\\uD83D\\uDD04 [PARALLEL] Refreshing chat history in background...\");\n                    refetchChatHistory(true);\n                    console.log(\"✅ [PARALLEL] Chat history refreshed\");\n                }\n            }).catch((refreshErr)=>{\n                console.error(\"❌ [PARALLEL] Chat history refresh failed:\", refreshErr);\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col transition-all duration-300 ease-in-out\",\n                style: {\n                    marginLeft: sidebarWidth,\n                    marginRight: isCanvasOpen && !isCanvasMinimized ? \"50%\" // Canvas takes 50% of screen width\n                     : isHistoryCollapsed ? \"0px\" : \"320px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isCanvasOpen && !isCanvasMinimized ? \"50%\" // Canvas takes 50% of screen width\n                             : isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: selectedConfigId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2015,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2016,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2020,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"Not Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2021,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2012,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>handleConfigChange(e.target.value),\n                                                        disabled: customConfigs.length === 0,\n                                                        className: \"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Router\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2032,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2034,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2026,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2041,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2040,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2039,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2025,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2011,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Streaming\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2049,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setUseStreaming(!useStreaming),\n                                                className: \"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm \".concat(useStreaming ? \"bg-orange-500 shadow-orange-200\" : \"bg-gray-300\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm \".concat(useStreaming ? \"translate-x-6\" : \"translate-x-1\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2056,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2050,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2048,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2009,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2008,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2002,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col pt-20 pb-32\",\n                        children: messages.length === 0 && !currentConversation ? /* Welcome Screen - Perfectly centered with no scroll */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center px-6 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mx-auto transition-all duration-300 \".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-2xl -ml-32\" : \"max-w-4xl\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Welcome to RoKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2077,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600 max-w-md mx-auto\",\n                                                    children: \"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2078,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2076,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 w-full max-w-2xl\",\n                                            children: conversationStarters.map((starter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleStarterClick(starter.prompt),\n                                                    disabled: !selectedConfigId,\n                                                    className: \"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed \".concat(!selectedConfigId ? \"cursor-not-allowed\" : \"cursor-pointer hover:scale-[1.02]\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-xl flex items-center justify-center text-xl \".concat(starter.color, \" group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: starter.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2095,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors\",\n                                                                            children: starter.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2099,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                            children: starter.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2102,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2098,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2094,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-orange-500\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 4l8 8-8 8M4 12h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2109,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2108,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2107,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, starter.id, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2086,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2084,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2075,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2072,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2071,\n                            columnNumber: 13\n                        }, this) : /* Chat Messages - Scrollable area with perfect centering */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesContainerRef,\n                                        className: \"w-full h-full overflow-y-auto px-6 transition-all duration-300 \".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-2xl -ml-32\" : \"max-w-4xl\"),\n                                        onScroll: handleScroll,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 py-8\",\n                                            children: [\n                                                currentConversation && messages.length >= 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>loadConversation(currentConversation, true),\n                                                        disabled: isLoadingHistory,\n                                                        className: \"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50\",\n                                                        children: isLoadingHistory ? \"Loading...\" : \"Load Earlier Messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2133,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2132,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoadingMessages && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: Array.from({\n                                                        length: 3\n                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2148,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2151,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2152,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-5/6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2153,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2150,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2149,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2147,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2145,\n                                                    columnNumber: 23\n                                                }, this),\n                                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\", \" group \").concat(isCanvasOpen && !isCanvasMinimized ? \"-ml-48\" : \"\", \" \").concat(msg.role === \"assistant\" && isCanvasOpen && !isCanvasMinimized ? \"ml-8\" : \"\"),\n                                                        children: [\n                                                            msg.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-orange-500\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2173,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2172,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2171,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-[80%]\" : \"max-w-[65%]\", \" relative \").concat(msg.role === \"user\" ? \"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm\" : msg.role === \"assistant\" ? \"card text-gray-900 rounded-2xl rounded-bl-lg\" : msg.role === \"system\" ? \"bg-amber-50 text-amber-800 rounded-xl border border-amber-200\" : \"bg-red-50 text-red-800 rounded-xl border border-red-200\", \" px-4 py-3 transition-all duration-300\"),\n                                                                children: [\n                                                                    msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\",\n                                                                                className: \"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2193,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditingMessage(msg.id, msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\")),\n                                                                                className: \"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer\",\n                                                                                title: \"Edit message\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"w-4 h-4 stroke-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2205,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2200,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2192,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    msg.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-8 left-0 z-10 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2213,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            msg.role === \"assistant\" && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                configId: selectedConfigId,\n                                                                                onRetry: (apiKeyId)=>handleRetryMessage(index, apiKeyId),\n                                                                                disabled: isLoading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2220,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2212,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2 chat-message-content\",\n                                                                        children: msg.role === \"user\" && editingMessageId === msg.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: editingText,\n                                                                                    onChange: (e)=>setEditingText(e.target.value),\n                                                                                    className: \"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none\",\n                                                                                    placeholder: \"Edit your message...\",\n                                                                                    rows: 3,\n                                                                                    autoFocus: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2233,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: saveEditedMessage,\n                                                                                            disabled: !editingText.trim(),\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2247,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Save & Continue\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2248,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2242,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: cancelEditingMessage,\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2254,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2255,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2250,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2241,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white/70 text-xs\",\n                                                                                    children: \"\\uD83D\\uDCA1 Saving will restart the conversation from this point, removing all messages that came after.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2258,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2232,\n                                                                            columnNumber: 23\n                                                                        }, this) : /* Normal message display */ msg.content.map((part, partIndex)=>{\n                                                                            if (part.type === \"text\") {\n                                                                                // Use LazyMarkdownRenderer for assistant messages, plain text for others\n                                                                                if (msg.role === \"assistant\") {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                        content: part.text,\n                                                                                        className: \"text-sm\"\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2269,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                } else {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"whitespace-pre-wrap break-words leading-relaxed text-sm\",\n                                                                                        children: part.text\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2277,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                }\n                                                                            }\n                                                                            if (part.type === \"image_url\") {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: part.image_url.url,\n                                                                                    alt: \"uploaded content\",\n                                                                                    className: \"max-w-full max-h-48 rounded-xl shadow-sm\"\n                                                                                }, partIndex, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2285,\n                                                                                    columnNumber: 29\n                                                                                }, this);\n                                                                            }\n                                                                            return null;\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2229,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2178,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-white\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2302,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2301,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2300,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, msg.id, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2162,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    currentStage: messageStatus.currentStage,\n                                                    isStreaming: useStreaming && messageStatus.currentStage === \"typing\",\n                                                    orchestrationStatus: orchestrationStatus,\n                                                    onStageChange: (stage)=>{\n                                                        console.log(\"\\uD83C\\uDFAF UI Status changed to: \".concat(stage));\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2310,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showOrchestration && orchestrationExecutionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OrchestrationCanvas__WEBPACK_IMPORTED_MODULE_6__.OrchestrationCanvas, {\n                                                    executionId: orchestrationExecutionId,\n                                                    onCanvasStateChange: handleCanvasStateChange,\n                                                    onComplete: (result)=>{\n                                                        // Skip auto-completion for test execution IDs\n                                                        if (orchestrationExecutionId === null || orchestrationExecutionId === void 0 ? void 0 : orchestrationExecutionId.startsWith(\"test-execution-id\")) {\n                                                            console.log(\"\\uD83C\\uDFAD [DEBUG] Skipping auto-completion for test execution\");\n                                                            return;\n                                                        }\n                                                        console.log(\"\\uD83C\\uDF89 [ORCHESTRATION] Completed:\", result);\n                                                        // Add the final result as a message\n                                                        const finalMessage = {\n                                                            id: Date.now().toString() + \"-orchestration-final\",\n                                                            role: \"assistant\",\n                                                            content: [\n                                                                {\n                                                                    type: \"text\",\n                                                                    text: result\n                                                                }\n                                                            ]\n                                                        };\n                                                        setMessages((prevMessages)=>[\n                                                                ...prevMessages,\n                                                                finalMessage\n                                                            ]);\n                                                        // Hide orchestration UI\n                                                        setShowOrchestration(false);\n                                                        setOrchestrationExecutionId(null);\n                                                        // Save final message\n                                                        if (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) {\n                                                            saveMessageToDatabase(currentConversation.id, finalMessage).catch((err)=>{\n                                                                console.error(\"❌ Failed to save orchestration final message:\", err);\n                                                            });\n                                                        }\n                                                    },\n                                                    onError: (error)=>{\n                                                        // Skip auto-close for test execution IDs\n                                                        if (orchestrationExecutionId === null || orchestrationExecutionId === void 0 ? void 0 : orchestrationExecutionId.startsWith(\"test-execution-id\")) {\n                                                            console.log(\"\\uD83C\\uDFAD [DEBUG] Ignoring test execution error:\", error);\n                                                            return;\n                                                        }\n                                                        console.error(\"❌ [ORCHESTRATION] Error:\", error);\n                                                        setError(\"Orchestration error: \".concat(error));\n                                                        setShowOrchestration(false);\n                                                        setOrchestrationExecutionId(null);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2129,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2122,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2121,\n                                    columnNumber: 15\n                                }, this),\n                                showScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToBottom(true),\n                                        className: \"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group\",\n                                        \"aria-label\": \"Scroll to bottom\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2383,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2382,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2377,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2376,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2120,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2068,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 z-50 bg-[#faf8f5]/95 backdrop-blur-sm border-t border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isCanvasOpen && !isCanvasMinimized ? \"50%\" // Canvas takes 50% of screen width\n                             : isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full transition-all duration-300 \".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-2xl\" : \"max-w-4xl\"),\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-red-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2408,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2407,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800 text-sm font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2410,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2406,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2405,\n                                        columnNumber: 17\n                                    }, this),\n                                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] Manual canvas test triggered\");\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] Current state before:\", {\n                                                        showOrchestration,\n                                                        orchestrationExecutionId\n                                                    });\n                                                    setOrchestrationExecutionId(\"test-execution-id-\" + Date.now());\n                                                    setShowOrchestration(true);\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] State should be updated now\");\n                                                    // Also test if OrchestrationCanvas component exists\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] OrchestrationCanvas component:\", _components_OrchestrationCanvas__WEBPACK_IMPORTED_MODULE_6__.OrchestrationCanvas);\n                                                },\n                                                className: \"px-4 py-2 bg-purple-500 text-white rounded-lg text-sm hover:bg-purple-600\",\n                                                children: \"\\uD83C\\uDFAD Test Canvas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2418,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    console.log(\"\\uD83C\\uDFAD [DEBUG] Manual canvas close triggered\");\n                                                    setShowOrchestration(false);\n                                                    setOrchestrationExecutionId(null);\n                                                },\n                                                className: \"px-4 py-2 bg-red-500 text-white rounded-lg text-sm hover:bg-red-600\",\n                                                children: \"❌ Close Canvas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2433,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2417,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSendMessage,\n                                        children: [\n                                            imagePreviews.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2452,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            imagePreviews.length,\n                                                                            \" image\",\n                                                                            imagePreviews.length > 1 ? \"s\" : \"\",\n                                                                            \" attached\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2453,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2451,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>handleRemoveImage(),\n                                                                className: \"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium\",\n                                                                children: \"Clear all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2457,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2450,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3\",\n                                                        children: imagePreviews.map((preview, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: preview,\n                                                                                alt: \"Preview \".concat(index + 1),\n                                                                                className: \"w-full h-full object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2469,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2474,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>handleRemoveImage(index),\n                                                                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100\",\n                                                                                \"aria-label\": \"Remove image \".concat(index + 1),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"w-3.5 h-3.5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2481,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2475,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2468,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2484,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2467,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2465,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2449,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-4 space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            multiple: true,\n                                                            onChange: handleImageChange,\n                                                            ref: fileInputRef,\n                                                            className: \"hidden\",\n                                                            id: \"imageUpload\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2497,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            disabled: imageFiles.length >= 10,\n                                                            className: \"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 \".concat(imageFiles.length >= 10 ? \"text-gray-300 cursor-not-allowed\" : \"text-gray-400 hover:text-orange-500 hover:bg-orange-50\"),\n                                                            \"aria-label\": imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images\",\n                                                            title: imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images (up to 10)\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2520,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                imageFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold\",\n                                                                    children: imageFiles.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2522,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2508,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: messageInput,\n                                                                onChange: (e)=>setMessageInput(e.target.value),\n                                                                placeholder: selectedConfigId ? \"Type a message...\" : \"Select a router first\",\n                                                                disabled: !selectedConfigId || isLoading,\n                                                                rows: 1,\n                                                                className: \"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                        e.preventDefault();\n                                                                        if ((messageInput.trim() || imageFiles.length > 0) && selectedConfigId && !isLoading) {\n                                                                            handleSendMessage();\n                                                                        }\n                                                                    }\n                                                                },\n                                                                style: {\n                                                                    minHeight: \"24px\",\n                                                                    maxHeight: \"120px\"\n                                                                },\n                                                                onInput: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.height = \"auto\";\n                                                                    target.style.height = Math.min(target.scrollHeight, 120) + \"px\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2530,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2529,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: !selectedConfigId || isLoading || !messageInput.trim() && imageFiles.length === 0,\n                                                            className: \"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0\",\n                                                            \"aria-label\": \"Send message\",\n                                                            title: \"Send message\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 animate-spin\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2564,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2563,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2567,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2555,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2495,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2494,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2446,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2400,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2399,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2393,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 1995,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 \".concat(isHistoryCollapsed ? \"w-0 overflow-hidden\" : \"w-80\"),\n                style: {\n                    transform: isHistoryCollapsed ? \"translateX(100%)\" : \"translateX(0)\",\n                    opacity: isHistoryCollapsed ? 0 : 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-orange-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2593,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2592,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2591,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2597,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        chatHistory.length,\n                                                        \" conversations\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2598,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2596,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2590,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsHistoryCollapsed(!isHistoryCollapsed),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105\",\n                                    \"aria-label\": \"Toggle history sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2607,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2606,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2601,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2589,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startNewChat,\n                                className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2619,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2618,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"New Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2621,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2614,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2613,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                            children: isLoadingHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-xl border border-gray-100 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-4 w-3/4 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2632,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-3 w-1/2 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2633,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2631,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2629,\n                                columnNumber: 15\n                            }, this) : chatHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2641,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2640,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2639,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2644,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Start chatting to see your history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2645,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2638,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: chatHistory.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatHistoryItem, {\n                                        chat: chat,\n                                        currentConversation: currentConversation,\n                                        onLoadChat: loadChatFromHistory,\n                                        onDeleteChat: deleteConversation\n                                    }, chat.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2650,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2627,\n                            columnNumber: 11\n                        }, this),\n                        isChatHistoryStale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-orange-50 border-t border-orange-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-xs text-orange-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 mr-1 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2667,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2666,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Updating...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2665,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2664,\n                            columnNumber: 13\n                        }, this),\n                        chatHistoryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-red-50 border-t border-red-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Failed to load history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2678,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>refetchChatHistory(true),\n                                        className: \"text-red-700 hover:text-red-800 font-medium\",\n                                        children: \"Retry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2679,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2677,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2676,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2587,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2581,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out \".concat(isHistoryCollapsed ? \"opacity-100 scale-100 translate-x-0\" : \"opacity-0 scale-95 translate-x-4 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsHistoryCollapsed(false),\n                    className: \"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105\",\n                    \"aria-label\": \"Show history sidebar\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2701,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2700,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2695,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2692,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 1993,\n        columnNumber: 5\n    }, this);\n}\n_s(PlaygroundPage, \"F/1IuKFPqYkhx9DgWk3c3+12tl8=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_7__.useSidebar,\n        _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_9__.useSmartMessageStatus,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistory,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch\n    ];\n});\n_c1 = PlaygroundPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatHistoryItem\");\n$RefreshReg$(_c1, \"PlaygroundPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/playground/page.tsx\n"));

/***/ })

});