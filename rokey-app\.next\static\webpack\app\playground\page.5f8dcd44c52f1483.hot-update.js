"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationCanvas.tsx":
/*!************************************************!*\
  !*** ./src/components/OrchestrationCanvas.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationCanvas: function() { return /* binding */ OrchestrationCanvas; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OrchestrationChatroom */ \"(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationCanvas = (param)=>{\n    let { executionId, onComplete, onError, onCanvasStateChange } = param;\n    _s();\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orchestrationComplete, setOrchestrationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { events, isConnected, error } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Handle orchestration completion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const synthesisCompleteEvent = events.find((event)=>event.type === \"synthesis_complete\");\n        if (synthesisCompleteEvent && !orchestrationComplete) {\n            var _synthesisCompleteEvent_data;\n            setOrchestrationComplete(true);\n            const result = ((_synthesisCompleteEvent_data = synthesisCompleteEvent.data) === null || _synthesisCompleteEvent_data === void 0 ? void 0 : _synthesisCompleteEvent_data.result) || \"Orchestration completed successfully\";\n            setFinalResult(result);\n            // Notify parent component\n            if (onComplete) {\n                onComplete(result);\n            }\n        }\n    }, [\n        events,\n        orchestrationComplete,\n        onComplete\n    ]);\n    // Handle errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (error && onError) {\n            onError(error);\n        }\n    }, [\n        error,\n        onError\n    ]);\n    const handleMinimize = ()=>{\n        setIsMinimized(true);\n        setIsCanvasOpen(false);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(false, true);\n    };\n    const handleMaximize = ()=>{\n        setIsMinimized(false);\n        setIsCanvasOpen(true);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(true, false);\n    };\n    const handleClose = ()=>{\n        setIsCanvasOpen(false);\n        setIsMinimized(false);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(false, false);\n    };\n    // Notify parent of initial canvas state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(isCanvasOpen, isMinimized);\n    }, [\n        isCanvasOpen,\n        isMinimized,\n        onCanvasStateChange\n    ]);\n    // Minimized card state - now returns null, will be rendered inline in chat\n    if (isMinimized) {\n        return null;\n    }\n    // Canvas is closed\n    if (!isCanvasOpen) {\n        return null;\n    }\n    // Debug log when rendering\n    console.log(\"\\uD83C\\uDFAD [DEBUG] OrchestrationCanvas is rendering!\", {\n        isCanvasOpen,\n        isMinimized,\n        executionId,\n        shouldBeVisible: isCanvasOpen && !isMinimized,\n        transformClass: isCanvasOpen ? \"translate-x-0\" : \"translate-x-full\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-0 right-0 h-full w-1/2 bg-white shadow-2xl z-[9999] transform transition-transform duration-300 ease-out border-l border-gray-200 \".concat(isCanvasOpen ? \"translate-x-0\" : \"translate-x-full\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-4 left-4 bg-green-500 text-white p-2 rounded z-[10000] text-xs font-bold\",\n                    children: [\n                        \"SPLIT-SCREEN CANVAS - isOpen: \",\n                        isCanvasOpen.toString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"AI Team Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: orchestrationComplete ? \"Orchestration Complete\" : \"Multi-Role Orchestration in Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleMinimize,\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200\",\n                                    \"aria-label\": \"Minimize canvas\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClose,\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200\",\n                                    \"aria-label\": \"Close canvas\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 h-full overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__.OrchestrationChatroom, {\n                        executionId: executionId,\n                        events: events,\n                        isConnected: isConnected,\n                        error: error,\n                        isComplete: orchestrationComplete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(OrchestrationCanvas, \"RDHLp+HVHmkFq5YA06E2O93JbzM=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = OrchestrationCanvas;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\n"));

/***/ })

});