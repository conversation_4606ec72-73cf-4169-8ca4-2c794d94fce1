"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/ChatMessage.tsx":
/*!****************************************!*\
  !*** ./src/components/ChatMessage.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatMessage: function() { return /* binding */ ChatMessage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ChatMessage auto */ \n\n\nconst ChatMessage = (param)=>{\n    let { message } = param;\n    const getRoleColor = (roleId)=>{\n        if (!roleId) return \"from-blue-500 to-blue-600\"; // Moderator\n        // Generate consistent colors based on role name\n        const colors = [\n            \"from-green-500 to-green-600\",\n            \"from-purple-500 to-purple-600\",\n            \"from-orange-500 to-orange-600\",\n            \"from-pink-500 to-pink-600\",\n            \"from-indigo-500 to-indigo-600\",\n            \"from-teal-500 to-teal-600\",\n            \"from-red-500 to-red-600\",\n            \"from-yellow-500 to-yellow-600\"\n        ];\n        const hash = roleId.split(\"\").reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n        return colors[hash % colors.length];\n    };\n    const getRoleIcon = (sender, roleId)=>{\n        if (sender === \"moderator\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                lineNumber: 48,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 50,\n            columnNumber: 12\n        }, undefined);\n    };\n    const getMessageTypeIcon = (type)=>{\n        switch(type){\n            case \"assignment\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, undefined);\n            case \"completion\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, undefined);\n            case \"handoff\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    const formatTime = (timestamp)=>{\n        return timestamp.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const isFromModerator = message.sender === \"moderator\";\n    const roleColor = getRoleColor(message.roleId);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex \".concat(isFromModerator ? \"justify-start\" : \"justify-start\", \" mb-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[85%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r \".concat(roleColor, \" flex items-center justify-center text-white shadow-sm\"),\n                    children: getRoleIcon(message.sender, message.roleId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-semibold \".concat(isFromModerator ? \"text-blue-700\" : \"text-gray-700\"),\n                                    children: message.senderName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined),\n                                getMessageTypeIcon(message.type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: getMessageTypeIcon(message.type)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block px-4 py-3 rounded-2xl shadow-sm \".concat(isFromModerator ? \"bg-blue-50 border border-blue-100\" : \"bg-gray-50 border border-gray-100\", \" \").concat(message.type === \"completion\" ? \"border-green-200 bg-green-50\" : message.type === \"assignment\" ? \"border-blue-200 bg-blue-50\" : message.type === \"handoff\" ? \"border-purple-200 bg-purple-50\" : message.type === \"clarification\" ? \"border-yellow-200 bg-yellow-50\" : \"\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm leading-relaxed \".concat(isFromModerator ? \"text-blue-900\" : \"text-gray-800\", \" \").concat(message.type === \"completion\" ? \"text-green-900\" : message.type === \"assignment\" ? \"text-blue-900\" : message.type === \"handoff\" ? \"text-purple-900\" : message.type === \"clarification\" ? \"text-yellow-900\" : \"\"),\n                                children: message.content.split(\"\\n\").map((line, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                        children: [\n                                            line,\n                                            index < message.content.split(\"\\n\").length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 70\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined),\n                        message.type !== \"message\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(message.type === \"assignment\" ? \"bg-blue-100 text-blue-800\" : message.type === \"completion\" ? \"bg-green-100 text-green-800\" : message.type === \"handoff\" ? \"bg-purple-100 text-purple-800\" : message.type === \"clarification\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-gray-100 text-gray-800\"),\n                                children: [\n                                    message.type === \"assignment\" && \"\\uD83D\\uDCCB Task Assignment\",\n                                    message.type === \"completion\" && \"✅ Task Complete\",\n                                    message.type === \"handoff\" && \"\\uD83D\\uDD04 Handoff\",\n                                    message.type === \"clarification\" && \"❓ Clarification\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatMessage.tsx\n"));

/***/ })

});