'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

export interface OrchestrationEvent {
  id: string;
  execution_id: string;
  type: string;
  timestamp: string;
  data: any;
  step_number?: number;
  role_id?: string;
  model_name?: string;
}

interface UseOrchestrationStreamReturn {
  events: OrchestrationEvent[];
  isConnected: boolean;
  error: string | null;
  lastEvent: OrchestrationEvent | null;
  reconnect: () => void;
  disconnect: () => void;
}

export function useOrchestrationStream(executionId: string, directStreamUrl?: string): UseOrchestrationStreamReturn {
  const [events, setEvents] = useState<OrchestrationEvent[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastEvent, setLastEvent] = useState<OrchestrationEvent | null>(null);
  const [streamUrl, setStreamUrl] = useState<string>('');

  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const baseReconnectDelay = 1000; // 1 second
  
  // Track the current execution ID and stream URL to detect changes
  const currentExecutionIdRef = useRef<string>('');
  const currentStreamUrlRef = useRef<string>('');

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setIsConnected(false);
  }, []);

  const connect = useCallback(() => {
    if (!executionId && !directStreamUrl) {
      setError('No execution ID or direct stream URL provided');
      return;
    }

    // Determine the URL to connect to
    const url = directStreamUrl || (executionId ? `/api/orchestration/stream/${executionId}` : '');
    
    if (!url) {
      setError('No valid stream URL could be determined');
      return;
    }
    
    // Skip if we're already connected to this URL
    if (currentStreamUrlRef.current === url && isConnected) {
      console.log(`[Orchestration Stream] Already connected to: ${url}`);
      return;
    }
    
    console.log(`🎭 [Orchestration Stream] Connecting to: ${url} with executionId: ${executionId}`);
    
    // Clean up existing connection
    disconnect();
    
    // Update refs
    currentExecutionIdRef.current = executionId || '';
    currentStreamUrlRef.current = url;

    try {
      const eventSource = new EventSource(url);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log(`[Orchestration Stream] Connected to execution ${executionId}`);
        setIsConnected(true);
        setError(null);
        reconnectAttempts.current = 0;
      };

      eventSource.onmessage = (event) => {
        try {
          const orchestrationEvent: OrchestrationEvent = JSON.parse(event.data);
          
          console.log(`[Orchestration Stream] Received event:`, orchestrationEvent);
          
          setEvents(prev => [...prev, orchestrationEvent]);
          setLastEvent(orchestrationEvent);
          
          // Reset error state on successful message
          setError(null);
        } catch (parseError) {
          console.error('[Orchestration Stream] Error parsing event:', parseError);
          setError('Error parsing stream data');
        }
      };

      // Handle specific event types
      eventSource.addEventListener('orchestration_started', (event) => {
        const data = JSON.parse(event.data);
        console.log('[Orchestration Stream] Orchestration started:', data);
      });

      eventSource.addEventListener('step_started', (event) => {
        const data = JSON.parse(event.data);
        console.log('[Orchestration Stream] Step started:', data);
      });

      eventSource.addEventListener('step_progress', (event) => {
        const data = JSON.parse(event.data);
        console.log('[Orchestration Stream] Step progress:', data);
      });

      eventSource.addEventListener('step_completed', (event) => {
        const data = JSON.parse(event.data);
        console.log('[Orchestration Stream] Step completed:', data);
      });

      eventSource.addEventListener('synthesis_started', (event) => {
        const data = JSON.parse(event.data);
        console.log('[Orchestration Stream] Synthesis started:', data);
      });

      eventSource.addEventListener('orchestration_completed', (event) => {
        const data = JSON.parse(event.data);
        console.log('[Orchestration Stream] Orchestration completed:', data);
      });

      eventSource.onerror = (event) => {
        console.error('[Orchestration Stream] Connection error:', event);
        setIsConnected(false);
        
        // Attempt to reconnect with exponential backoff
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = baseReconnectDelay * Math.pow(2, reconnectAttempts.current);
          reconnectAttempts.current++;
          
          setError(`Connection lost. Reconnecting in ${delay/1000}s... (attempt ${reconnectAttempts.current}/${maxReconnectAttempts})`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`[Orchestration Stream] Reconnecting... (attempt ${reconnectAttempts.current})`);
            connect();
          }, delay);
        } else {
          setError('Connection failed after multiple attempts. Please refresh the page.');
        }
      };

    } catch (connectionError) {
      console.error('[Orchestration Stream] Failed to create connection:', connectionError);
      setError('Failed to establish connection');
      setIsConnected(false);
    }
  }, [executionId, disconnect]);

  const reconnect = useCallback(() => {
    reconnectAttempts.current = 0;
    connect();
  }, [connect]);

  // Connect on mount and when executionId or directStreamUrl changes
  useEffect(() => {
    if (executionId || directStreamUrl) {
      connect();
    }

    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [executionId, directStreamUrl, connect, disconnect]);

  // Handle page visibility changes to reconnect when page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !isConnected && (executionId || directStreamUrl)) {
        console.log('[Orchestration Stream] Page became visible, attempting to reconnect...');
        reconnect();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isConnected, executionId, directStreamUrl, reconnect]);

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      if (!isConnected && (executionId || directStreamUrl)) {
        console.log('[Orchestration Stream] Network came back online, attempting to reconnect...');
        reconnect();
      }
    };

    const handleOffline = () => {
      setError('Network connection lost');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isConnected, executionId, directStreamUrl, reconnect]);

  return {
    events,
    isConnected,
    error,
    lastEvent,
    reconnect,
    disconnect
  };
}

// Helper hook for filtering events by type
export function useOrchestrationEventsByType(
  executionId: string,
  eventType: string
): OrchestrationEvent[] {
  const { events } = useOrchestrationStream(executionId);
  
  return events.filter(event => event.type === eventType);
}

// Helper hook for getting the latest event of a specific type
export function useLatestOrchestrationEvent(
  executionId: string,
  eventType: string
): OrchestrationEvent | null {
  const { events } = useOrchestrationStream(executionId);
  
  const filteredEvents = events.filter(event => event.type === eventType);
  return filteredEvents.length > 0 ? filteredEvents[filteredEvents.length - 1] : null;
}

// Helper hook for tracking orchestration progress
export function useOrchestrationProgress(executionId: string): {
  totalSteps: number;
  completedSteps: number;
  currentStep: number;
  progress: number;
  isComplete: boolean;
} {
  const { events } = useOrchestrationStream(executionId);
  
  const stepStartedEvents = events.filter(e => e.type === 'step_started');
  const stepCompletedEvents = events.filter(e => e.type === 'step_completed');
  const orchestrationCompleted = events.some(e => e.type === 'orchestration_completed');
  
  const totalSteps = stepStartedEvents.length;
  const completedSteps = stepCompletedEvents.length;
  const currentStep = totalSteps > 0 ? Math.min(completedSteps + 1, totalSteps) : 0;
  const progress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
  
  return {
    totalSteps,
    completedSteps,
    currentStep,
    progress,
    isComplete: orchestrationCompleted
  };
}
