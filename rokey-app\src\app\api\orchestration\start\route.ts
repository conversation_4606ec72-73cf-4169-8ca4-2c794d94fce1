import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { z } from 'zod';
import { broadcastOrchestrationEvent } from '@/lib/orchestration-stream-manager';

// Schema for the request body
const StartOrchestrationSchema = z.object({
  executionId: z.string().uuid(),
});

export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientOnRequest();

  try {
    console.log(`[Conversational Orchestration] Starting conversational orchestration...`);

    // Parse and validate the request body
    const body = await request.json();
    const validatedBody = StartOrchestrationSchema.parse(body);
    const executionId = validatedBody.executionId;

    console.log(`[Conversational Orchestration] Starting execution: ${executionId}`);

    // Get the execution record with original request
    const { data: execution, error: executionError } = await supabase
      .from('orchestration_executions')
      .select('*')
      .eq('id', executionId)
      .single();

    if (executionError || !execution) {
      return NextResponse.json(
        { error: 'Orchestration execution not found' },
        { status: 404 }
      );
    }

    // Get the planned steps to understand the team composition
    const { data: steps, error: stepsError } = await supabase
      .from('orchestration_steps')
      .select('*')
      .eq('execution_id', executionId)
      .order('step_number');

    if (stepsError || !steps || steps.length === 0) {
      return NextResponse.json(
        { error: 'No orchestration steps found' },
        { status: 404 }
      );
    }

    console.log(`[Conversational Orchestration] Found ${steps.length} specialists for the team`);

    // Update execution status to in_progress
    await supabase
      .from('orchestration_executions')
      .update({ status: 'in_progress' })
      .eq('id', executionId);

    // Start the conversational orchestration flow
    await startConversationalOrchestration(executionId, execution.original_prompt, steps);

    return NextResponse.json({
      success: true,
      execution: {
        id: execution.id,
        status: 'in_progress',
        message: 'Conversational orchestration started'
      }
    });
  } catch (error) {
    console.error(`[Conversational Orchestration] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error', details: String(error) },
      { status: 500 }
    );
  }
}

// NEW CONVERSATIONAL ORCHESTRATION SYSTEM
async function startConversationalOrchestration(executionId: string, originalRequest: string, steps: any[]) {
  console.log(`🤖 [MODERATOR] Starting conversational orchestration for: "${originalRequest}"`);

  // 1. REAL AI MODERATOR generates introduction
  const moderatorIntroduction = await generateModeratorMessage(originalRequest, steps, 'introduction');
  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'moderator_introduction',
    timestamp: new Date().toISOString(),
    data: {
      speaker: 'moderator',
      message: moderatorIntroduction
    }
  });

  // Small delay for dramatic effect
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Define role emojis
  const roleEmojis: { [key: string]: string } = {
    'brainstorming_ideation': '🧠',
    'coding_backend': '💻',
    'coding_frontend': '🎨',
    'education_tutoring': '🎓',
    'research_analysis': '🔬',
    'creative_writing': '✍️',
    'business_strategy': '📊',
    'technical_documentation': '📚'
  };

  // 2. Introduce each specialist
  for (let i = 0; i < steps.length; i++) {
    const step = steps[i];
    const emoji = roleEmojis[step.role_id] || '🤖';
    const roleName = step.role_id.replace('_', ' ').toUpperCase();

    await broadcastOrchestrationEvent(executionId, {
      id: crypto.randomUUID(),
      execution_id: executionId,
      type: 'specialist_introduction',
      timestamp: new Date().toISOString(),
      data: {
        speaker: 'moderator',
        specialist: step.role_id,
        message: `🤖 **AI Moderator**: "Meet our ${emoji} **${roleName} Specialist** - they'll handle step ${step.step_number} of our process."`
      }
    });

    await new Promise(resolve => setTimeout(resolve, 800));
  }

  // 3. Start the conversation with the first specialist
  const firstStep = steps[0];
  const firstEmoji = roleEmojis[firstStep.role_id] || '🤖';
  const firstRoleName = firstStep.role_id.replace('_', ' ').toUpperCase();

  // REAL AI MODERATOR assigns first task
  const firstAssignmentMessage = await generateModeratorMessage(originalRequest, steps, 'assignment', { specialist: firstStep.role_id });
  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'moderator_assignment',
    timestamp: new Date().toISOString(),
    data: {
      speaker: 'moderator',
      target_specialist: firstStep.role_id,
      message: firstAssignmentMessage
    }
  });

  // 4. Start processing the first step conversationally
  setTimeout(() => {
    processStepConversationally(executionId, firstStep, steps, 0, originalRequest);
  }, 2000);
}

// Process each step with conversational flow
async function processStepConversationally(executionId: string, currentStep: any, allSteps: any[], stepIndex: number, originalRequest?: string) {
  const roleEmojis: { [key: string]: string } = {
    'brainstorming_ideation': '🧠',
    'coding_backend': '💻',
    'coding_frontend': '🎨',
    'education_tutoring': '🎓',
    'research_analysis': '🔬',
    'creative_writing': '✍️',
    'business_strategy': '📊',
    'technical_documentation': '📚'
  };

  const emoji = roleEmojis[currentStep.role_id] || '🤖';
  const roleName = currentStep.role_id.replace('_', ' ').toUpperCase();

  console.log(`${emoji} [${roleName}] Starting work on step ${currentStep.step_number}`);

  // 1. REAL AI SPECIALIST acknowledges the assignment
  const acknowledgmentMessage = await generateSpecialistMessage(currentStep.role_id, originalRequest, 'acknowledgment');
  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'specialist_acknowledgment',
    timestamp: new Date().toISOString(),
    data: {
      speaker: currentStep.role_id,
      message: acknowledgmentMessage
    }
  });

  await new Promise(resolve => setTimeout(resolve, 1500));

  // 2. REAL AI SPECIALIST starts working
  const workingMessage = await generateSpecialistMessage(currentStep.role_id, originalRequest, 'working');
  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'specialist_working',
    timestamp: new Date().toISOString(),
    data: {
      speaker: currentStep.role_id,
      message: workingMessage
    }
  });

  // 3. Actually process the step (call the AI)
  try {
    const response = await callSpecialistAI(currentStep, allSteps, stepIndex);

    // 4. Specialist shares their work
    await broadcastOrchestrationEvent(executionId, {
      id: crypto.randomUUID(),
      execution_id: executionId,
      type: 'specialist_response',
      timestamp: new Date().toISOString(),
      data: {
        speaker: currentStep.role_id,
        message: `${emoji} **${roleName} Specialist**: "✅ I've completed my analysis! Here's my contribution:"\n\n${response}`,
        response: response
      }
    });

    // 5. Moderator reviews and transitions
    await new Promise(resolve => setTimeout(resolve, 2000));

    if (stepIndex < allSteps.length - 1) {
      // More steps to go - hand off to next specialist
      const nextStep = allSteps[stepIndex + 1];
      const nextEmoji = roleEmojis[nextStep.role_id] || '🤖';
      const nextRoleName = nextStep.role_id.replace('_', ' ').toUpperCase();

      // REAL AI MODERATOR generates handoff message
      const handoffMessage = await generateModeratorMessage(originalRequest || '', [], 'handoff', {
        fromSpecialist: currentStep.role_id,
        toSpecialist: nextStep.role_id
      });

      await broadcastOrchestrationEvent(executionId, {
        id: crypto.randomUUID(),
        execution_id: executionId,
        type: 'moderator_handoff',
        timestamp: new Date().toISOString(),
        data: {
          speaker: 'moderator',
          from_specialist: currentStep.role_id,
          to_specialist: nextStep.role_id,
          message: handoffMessage
        }
      });

      // Continue with next step
      setTimeout(() => {
        processStepConversationally(executionId, nextStep, allSteps, stepIndex + 1, originalRequest);
      }, 3000);

    } else {
      // All steps complete - start synthesis
      await broadcastOrchestrationEvent(executionId, {
        id: crypto.randomUUID(),
        execution_id: executionId,
        type: 'moderator_synthesis_start',
        timestamp: new Date().toISOString(),
        data: {
          speaker: 'moderator',
          message: `🤖 **AI Moderator**: "Outstanding work, team! All ${allSteps.length} specialists have contributed their expertise. Now I'll synthesize everything into a comprehensive response."`
        }
      });

      // Start synthesis
      setTimeout(() => {
        startConversationalSynthesis(executionId, allSteps);
      }, 2000);
    }

  } catch (error) {
    console.error(`${emoji} [${roleName}] Error processing step:`, error);

    await broadcastOrchestrationEvent(executionId, {
      id: crypto.randomUUID(),
      execution_id: executionId,
      type: 'specialist_error',
      timestamp: new Date().toISOString(),
      data: {
        speaker: currentStep.role_id,
        message: `${emoji} **${roleName} Specialist**: "❌ I encountered an error while processing this request. Let me try a different approach..."`
      }
    });
  }
}

// Call the actual AI for the specialist
async function callSpecialistAI(step: any, allSteps: any[], stepIndex: number): Promise<string> {
  // This will call the actual AI provider - for now, return a placeholder
  // TODO: Implement actual AI calling logic
  return `[Specialist AI Response for ${step.role_id} - Step ${step.step_number}]`;
}

// REAL AI MODERATOR MESSAGE GENERATION
async function generateModeratorMessage(originalRequest: string, steps: any[], messageType: string, context?: any): Promise<string> {
  const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
  if (!classificationApiKey) {
    throw new Error('Classification API key not found');
  }

  let prompt = '';

  switch (messageType) {
    case 'introduction':
      prompt = `You are an AI Moderator leading a team of ${steps.length} AI specialists to handle this request: "${originalRequest}"

Your specialists are: ${steps.map(s => s.role_id.replace('_', ' ')).join(', ')}

Generate a natural, conversational introduction message where you:
1. Greet the team professionally but warmly
2. Briefly explain the user's request
3. Mention that you've assembled the right specialists
4. Set an encouraging, collaborative tone

Keep it concise (2-3 sentences max). Be natural and conversational, not robotic.`;
      break;

    case 'assignment':
      prompt = `You are an AI Moderator assigning a task to the ${context.specialist} specialist.

Original request: "${originalRequest}"
Specialist role: ${context.specialist}

Generate a natural assignment message where you:
1. Address the specialist directly
2. Explain what you need them to focus on
3. Encourage them to be thorough
4. Keep it conversational and supportive

Keep it concise (1-2 sentences). Be natural, not templated.`;
      break;

    case 'handoff':
      prompt = `You are an AI Moderator handing off work from ${context.fromSpecialist} to ${context.toSpecialist}.

Previous specialist: ${context.fromSpecialist}
Next specialist: ${context.toSpecialist}
Original request: "${originalRequest}"

Generate a natural handoff message where you:
1. Acknowledge the previous specialist's good work
2. Introduce the next specialist
3. Explain how they should build on the previous work
4. Keep it encouraging and collaborative

Keep it concise (2-3 sentences). Be natural and conversational.`;
      break;

    case 'synthesis':
      prompt = `You are an AI Moderator starting to synthesize the work of ${context.stepCount} specialists.

Generate a natural message where you:
1. Acknowledge that all specialists have completed their work
2. Explain that you're now combining their insights
3. Set expectation for a comprehensive final response
4. Keep it encouraging and professional

Keep it concise (1-2 sentences). Be natural and conversational.`;
      break;

    case 'completion':
      prompt = `You are an AI Moderator completing a team collaboration with ${context.stepCount} specialists.

Generate a natural completion message where you:
1. Celebrate the successful collaboration
2. Indicate that the comprehensive response is ready
3. Thank the team for their expertise
4. Keep it positive and professional

Keep it concise (1-2 sentences). Be natural and conversational.`;
      break;
  }

  try {
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-001',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 200
      })
    });

    if (!response.ok) {
      throw new Error(`Moderator AI error: ${response.status}`);
    }

    const data = await response.json();
    const message = data.choices?.[0]?.message?.content || 'Hello team! Let\'s work together on this request.';

    console.log(`🤖 [MODERATOR AI] Generated ${messageType} message:`, message);
    return `🤖 **AI Moderator**: "${message}"`;

  } catch (error) {
    console.error(`🤖 [MODERATOR AI] Error generating ${messageType} message:`, error);
    return `🤖 **AI Moderator**: "Hello team! Let's collaborate on this request."`;
  }
}

// REAL AI SPECIALIST MESSAGE GENERATION
async function generateSpecialistMessage(specialist: string, originalRequest: string, messageType: string, context?: any): Promise<string> {
  const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
  if (!classificationApiKey) {
    throw new Error('Classification API key not found');
  }

  const roleEmojis: { [key: string]: string } = {
    'brainstorming_ideation': '🧠',
    'coding_backend': '💻',
    'coding_frontend': '🎨',
    'education_tutoring': '🎓',
    'research_analysis': '🔬',
    'creative_writing': '✍️',
    'business_strategy': '📊',
    'technical_documentation': '📚'
  };

  const emoji = roleEmojis[specialist] || '🤖';
  const roleName = specialist.replace('_', ' ').toUpperCase();

  let prompt = '';

  switch (messageType) {
    case 'acknowledgment':
      prompt = `You are a ${roleName} AI specialist. The moderator just assigned you to work on: "${originalRequest}"

Generate a brief, natural acknowledgment message where you:
1. Show you understand the assignment
2. Express readiness to help
3. Briefly mention your expertise area
4. Keep it professional but friendly

Keep it very concise (1-2 sentences). Be natural and conversational.`;
      break;

    case 'working':
      prompt = `You are a ${roleName} AI specialist currently working on: "${originalRequest}"

Generate a brief status update message where you:
1. Show you're actively working
2. Mention what you're analyzing/doing
3. Keep it engaging but professional

Keep it very concise (1 sentence). Be natural and show progress.`;
      break;
  }

  try {
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-001',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.8,
        max_tokens: 100
      })
    });

    if (!response.ok) {
      throw new Error(`Specialist AI error: ${response.status}`);
    }

    const data = await response.json();
    const message = data.choices?.[0]?.message?.content || 'I understand! Working on it now.';

    console.log(`${emoji} [${roleName} AI] Generated ${messageType} message:`, message);
    return `${emoji} **${roleName} Specialist**: "${message}"`;

  } catch (error) {
    console.error(`${emoji} [${roleName} AI] Error generating ${messageType} message:`, error);
    return `${emoji} **${roleName} Specialist**: "I understand the assignment! Working on it now."`;
  }
}

// Start conversational synthesis
async function startConversationalSynthesis(executionId: string, allSteps: any[]) {
  console.log(`🤖 [MODERATOR] Starting conversational synthesis for ${allSteps.length} specialist responses`);

  // REAL AI MODERATOR generates synthesis message
  const synthesisMessage = await generateModeratorMessage('', allSteps, 'synthesis', { stepCount: allSteps.length });

  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'synthesis_progress',
    timestamp: new Date().toISOString(),
    data: {
      speaker: 'moderator',
      message: synthesisMessage
    }
  });

  // TODO: Implement actual synthesis logic
  // For now, just complete the orchestration
  setTimeout(async () => {
    const completionMessage = await generateModeratorMessage('', allSteps, 'completion', { stepCount: allSteps.length });

    await broadcastOrchestrationEvent(executionId, {
      id: crypto.randomUUID(),
      execution_id: executionId,
      type: 'orchestration_complete',
      timestamp: new Date().toISOString(),
      data: {
        speaker: 'moderator',
        message: completionMessage
      }
    });
  }, 3000);
}