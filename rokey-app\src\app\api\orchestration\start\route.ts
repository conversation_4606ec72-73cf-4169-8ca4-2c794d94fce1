import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { z } from 'zod';
import { broadcastOrchestrationEvent } from '../stream/[executionId]/route';

// Schema for the request body
const StartOrchestrationSchema = z.object({
  executionId: z.string().uuid(),
});

export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientOnRequest();

  try {
    console.log(`[Conversational Orchestration] Starting conversational orchestration...`);

    // Parse and validate the request body
    const body = await request.json();
    const validatedBody = StartOrchestrationSchema.parse(body);
    const executionId = validatedBody.executionId;

    console.log(`[Conversational Orchestration] Starting execution: ${executionId}`);

    // Get the execution record with original request
    const { data: execution, error: executionError } = await supabase
      .from('orchestration_executions')
      .select('*')
      .eq('id', executionId)
      .single();

    if (executionError || !execution) {
      return NextResponse.json(
        { error: 'Orchestration execution not found' },
        { status: 404 }
      );
    }

    // Get the planned steps to understand the team composition
    const { data: steps, error: stepsError } = await supabase
      .from('orchestration_steps')
      .select('*')
      .eq('execution_id', executionId)
      .order('step_number');

    if (stepsError || !steps || steps.length === 0) {
      return NextResponse.json(
        { error: 'No orchestration steps found' },
        { status: 404 }
      );
    }

    console.log(`[Conversational Orchestration] Found ${steps.length} specialists for the team`);

    // Update execution status to in_progress
    await supabase
      .from('orchestration_executions')
      .update({ status: 'in_progress' })
      .eq('id', executionId);

    // Start the conversational orchestration flow
    await startConversationalOrchestration(executionId, execution.original_request, steps);

    return NextResponse.json({
      success: true,
      execution: {
        id: execution.id,
        status: 'in_progress',
        message: 'Conversational orchestration started'
      }
    });
  } catch (error) {
    console.error(`[Conversational Orchestration] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error', details: String(error) },
      { status: 500 }
    );
  }
}

// NEW CONVERSATIONAL ORCHESTRATION SYSTEM
async function startConversationalOrchestration(executionId: string, originalRequest: string, steps: any[]) {
  console.log(`🤖 [MODERATOR] Starting conversational orchestration for: "${originalRequest}"`);

  // 1. Moderator introduces the team
  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'moderator_introduction',
    timestamp: new Date().toISOString(),
    data: {
      speaker: 'moderator',
      message: `🤖 **AI Moderator**: "Hello team! I've analyzed the request: '${originalRequest}'. I've assembled ${steps.length} specialists to work together on this. Let me introduce everyone..."`
    }
  });

  // Small delay for dramatic effect
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 2. Introduce each specialist
  for (let i = 0; i < steps.length; i++) {
    const step = steps[i];
    const roleEmojis: { [key: string]: string } = {
      'brainstorming_ideation': '🧠',
      'coding_backend': '💻',
      'coding_frontend': '🎨',
      'education_tutoring': '🎓',
      'research_analysis': '🔬',
      'creative_writing': '✍️',
      'business_strategy': '📊',
      'technical_documentation': '📚'
    };

    const emoji = roleEmojis[step.role_id] || '🤖';
    const roleName = step.role_id.replace('_', ' ').toUpperCase();

    await broadcastOrchestrationEvent(executionId, {
      id: crypto.randomUUID(),
      execution_id: executionId,
      type: 'specialist_introduction',
      timestamp: new Date().toISOString(),
      data: {
        speaker: 'moderator',
        specialist: step.role_id,
        message: `🤖 **AI Moderator**: "Meet our ${emoji} **${roleName} Specialist** - they'll handle step ${step.step_number} of our process."`
      }
    });

    await new Promise(resolve => setTimeout(resolve, 800));
  }

  // 3. Start the conversation with the first specialist
  const firstStep = steps[0];
  const firstEmoji = roleEmojis[firstStep.role_id] || '🤖';
  const firstRoleName = firstStep.role_id.replace('_', ' ').toUpperCase();

  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'moderator_assignment',
    timestamp: new Date().toISOString(),
    data: {
      speaker: 'moderator',
      target_specialist: firstStep.role_id,
      message: `🤖 **AI Moderator** → ${firstEmoji} **${firstRoleName}**: "You're up first! Please analyze this request and provide your specialized expertise. Take your time and be thorough."`
    }
  });

  // 4. Start processing the first step conversationally
  setTimeout(() => {
    processStepConversationally(executionId, firstStep, steps, 0);
  }, 2000);
}

// Process each step with conversational flow
async function processStepConversationally(executionId: string, currentStep: any, allSteps: any[], stepIndex: number) {
  const roleEmojis: { [key: string]: string } = {
    'brainstorming_ideation': '🧠',
    'coding_backend': '💻',
    'coding_frontend': '🎨',
    'education_tutoring': '🎓',
    'research_analysis': '🔬',
    'creative_writing': '✍️',
    'business_strategy': '📊',
    'technical_documentation': '📚'
  };

  const emoji = roleEmojis[currentStep.role_id] || '🤖';
  const roleName = currentStep.role_id.replace('_', ' ').toUpperCase();

  console.log(`${emoji} [${roleName}] Starting work on step ${currentStep.step_number}`);

  // 1. Specialist acknowledges the assignment
  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'specialist_acknowledgment',
    timestamp: new Date().toISOString(),
    data: {
      speaker: currentStep.role_id,
      message: `${emoji} **${roleName} Specialist**: "I understand the assignment! Let me analyze this and provide my specialized expertise. Working on it now..."`
    }
  });

  await new Promise(resolve => setTimeout(resolve, 1500));

  // 2. Specialist starts working
  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'specialist_working',
    timestamp: new Date().toISOString(),
    data: {
      speaker: currentStep.role_id,
      message: `${emoji} **${roleName} Specialist**: "🔄 Analyzing the requirements and applying my specialized knowledge..."`
    }
  });

  // 3. Actually process the step (call the AI)
  try {
    const response = await callSpecialistAI(currentStep, allSteps, stepIndex);

    // 4. Specialist shares their work
    await broadcastOrchestrationEvent(executionId, {
      id: crypto.randomUUID(),
      execution_id: executionId,
      type: 'specialist_response',
      timestamp: new Date().toISOString(),
      data: {
        speaker: currentStep.role_id,
        message: `${emoji} **${roleName} Specialist**: "✅ I've completed my analysis! Here's my contribution:"\n\n${response}`,
        response: response
      }
    });

    // 5. Moderator reviews and transitions
    await new Promise(resolve => setTimeout(resolve, 2000));

    if (stepIndex < allSteps.length - 1) {
      // More steps to go - hand off to next specialist
      const nextStep = allSteps[stepIndex + 1];
      const nextEmoji = roleEmojis[nextStep.role_id] || '🤖';
      const nextRoleName = nextStep.role_id.replace('_', ' ').toUpperCase();

      await broadcastOrchestrationEvent(executionId, {
        id: crypto.randomUUID(),
        execution_id: executionId,
        type: 'moderator_handoff',
        timestamp: new Date().toISOString(),
        data: {
          speaker: 'moderator',
          from_specialist: currentStep.role_id,
          to_specialist: nextStep.role_id,
          message: `🤖 **AI Moderator**: "Excellent work, ${emoji} ${roleName}! Now ${nextEmoji} **${nextRoleName}**, it's your turn. Please build upon this foundation with your expertise."`
        }
      });

      // Continue with next step
      setTimeout(() => {
        processStepConversationally(executionId, nextStep, allSteps, stepIndex + 1);
      }, 3000);

    } else {
      // All steps complete - start synthesis
      await broadcastOrchestrationEvent(executionId, {
        id: crypto.randomUUID(),
        execution_id: executionId,
        type: 'moderator_synthesis_start',
        timestamp: new Date().toISOString(),
        data: {
          speaker: 'moderator',
          message: `🤖 **AI Moderator**: "Outstanding work, team! All ${allSteps.length} specialists have contributed their expertise. Now I'll synthesize everything into a comprehensive response."`
        }
      });

      // Start synthesis
      setTimeout(() => {
        startConversationalSynthesis(executionId, allSteps);
      }, 2000);
    }

  } catch (error) {
    console.error(`${emoji} [${roleName}] Error processing step:`, error);

    await broadcastOrchestrationEvent(executionId, {
      id: crypto.randomUUID(),
      execution_id: executionId,
      type: 'specialist_error',
      timestamp: new Date().toISOString(),
      data: {
        speaker: currentStep.role_id,
        message: `${emoji} **${roleName} Specialist**: "❌ I encountered an error while processing this request. Let me try a different approach..."`
      }
    });
  }
}

// Call the actual AI for the specialist
async function callSpecialistAI(step: any, allSteps: any[], stepIndex: number): Promise<string> {
  // This will call the actual AI provider - for now, return a placeholder
  // TODO: Implement actual AI calling logic
  return `[Specialist AI Response for ${step.role_id} - Step ${step.step_number}]`;
}

// Start conversational synthesis
async function startConversationalSynthesis(executionId: string, allSteps: any[]) {
  console.log(`🤖 [MODERATOR] Starting conversational synthesis for ${allSteps.length} specialist responses`);

  await broadcastOrchestrationEvent(executionId, {
    id: crypto.randomUUID(),
    execution_id: executionId,
    type: 'synthesis_progress',
    timestamp: new Date().toISOString(),
    data: {
      speaker: 'moderator',
      message: `🤖 **AI Moderator**: "🧩 Now I'm weaving together all the specialist insights into a cohesive, comprehensive response..."`
    }
  });

  // TODO: Implement actual synthesis logic
  // For now, just complete the orchestration
  setTimeout(async () => {
    await broadcastOrchestrationEvent(executionId, {
      id: crypto.randomUUID(),
      execution_id: executionId,
      type: 'orchestration_complete',
      timestamp: new Date().toISOString(),
      data: {
        speaker: 'moderator',
        message: `🤖 **AI Moderator**: "🎉 Synthesis complete! The team has delivered a comprehensive response combining all specialist expertise."`
      }
    });
  }, 3000);
}