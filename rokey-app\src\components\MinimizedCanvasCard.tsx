'use client';

import React from 'react';
import {
  ChatBubbleLeftRightIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface MinimizedCanvasCardProps {
  orchestrationComplete: boolean;
  onMaximize: () => void;
  isCanvasOpen?: boolean;
  isCanvasMinimized?: boolean;
}

export const MinimizedCanvasCard: React.FC<MinimizedCanvasCardProps> = ({
  orchestrationComplete,
  onMaximize,
  isCanvasOpen,
  isCanvasMinimized
}) => {
  return (
    <div className={`flex justify-start group ${
      isCanvasOpen && !isCanvasMinimized ? '-ml-96' : ''
    }`}>
      {/* Assistant Avatar */}
      <div className="w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100">
        <svg className="w-3.5 h-3.5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      </div>

      {/* Minimized Canvas Card */}
      <div className={`${
        isCanvasOpen && !isCanvasMinimized ? 'max-w-[80%]' : 'max-w-[65%]'
      } relative`}>
        <div 
          onClick={onMaximize}
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl rounded-bl-lg shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-[1.02] min-w-[320px]"
        >
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <ChatBubbleLeftRightIcon className="w-6 h-6" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm">AI Team Collaboration</h3>
              <p className="text-xs opacity-90">
                {orchestrationComplete ? 'Completed - Click to view results' : 'Multi-Role Orchestration in progress'}
              </p>
            </div>
            <div className="flex-shrink-0">
              {!orchestrationComplete && (
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
              )}
              {orchestrationComplete && (
                <SparklesIcon className="w-5 h-5" />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
