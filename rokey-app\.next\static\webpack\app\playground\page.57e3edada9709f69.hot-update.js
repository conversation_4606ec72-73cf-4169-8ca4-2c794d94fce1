"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/app/playground/page.tsx":
/*!*************************************!*\
  !*** ./src/app/playground/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PlaygroundPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperClipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,PaperClipIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=PencilSquareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilSquareIcon.js\");\n/* harmony import */ var _components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LazyMarkdownRenderer */ \"(app-pages-browser)/./src/components/LazyMarkdownRenderer.tsx\");\n/* harmony import */ var _components_CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* harmony import */ var _components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/RetryDropdown */ \"(app-pages-browser)/./src/components/RetryDropdown.tsx\");\n/* harmony import */ var _components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DynamicStatusIndicator */ \"(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\");\n/* harmony import */ var _components_OrchestrationCanvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/OrchestrationCanvas */ \"(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\");\n/* harmony import */ var _components_MinimizedCanvasCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/MinimizedCanvasCard */ \"(app-pages-browser)/./src/components/MinimizedCanvasCard.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useMessageStatus */ \"(app-pages-browser)/./src/hooks/useMessageStatus.ts\");\n/* harmony import */ var _utils_performanceLogs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/performanceLogs */ \"(app-pages-browser)/./src/utils/performanceLogs.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Temporarily comment out to fix import issue\n// import { ChatHistorySkeleton, EnhancedChatHistorySkeleton, MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';\n\n\n// import VirtualChatHistory from '@/components/VirtualChatHistory';\n// Import performance logging utilities for browser console access\n\n// Memoized chat history item component to prevent unnecessary re-renders\nconst ChatHistoryItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { chat, currentConversation, onLoadChat, onDeleteChat } = param;\n    const isActive = (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === chat.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 \".concat(isActive ? \"bg-orange-50 border border-orange-200\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onLoadChat(chat),\n                className: \"w-full text-left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 truncate mb-1\",\n                                children: chat.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined),\n                            chat.last_message_preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 line-clamp-2 mb-2\",\n                                children: chat.last_message_preview\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            chat.message_count,\n                                            \" messages\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: new Date(chat.updated_at).toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    onDeleteChat(chat.id);\n                },\n                className: \"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200\",\n                title: \"Delete conversation\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n});\n_c = ChatHistoryItem;\nfunction PlaygroundPage() {\n    _s();\n    const { isCollapsed, isHovered, setHoverDisabled } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_8__.useSidebar)();\n    // Calculate actual sidebar width (collapsed but can expand on hover)\n    const sidebarWidth = !isCollapsed || isHovered ? \"256px\" : \"64px\";\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialPageLoad, setInitialPageLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Prefetch API keys when config is selected for faster retry dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId) {\n            // Prefetch keys in background for retry dropdown\n            fetch(\"/api/keys?custom_config_id=\".concat(selectedConfigId)).then((response)=>response.json()).catch((error)=>console.log(\"Background key prefetch failed:\", error));\n        }\n    }, [\n        selectedConfigId\n    ]);\n    const [messageInput, setMessageInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useStreaming, setUseStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showScrollToBottom, setShowScrollToBottom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for multiple image handling (up to 10 images)\n    const [imageFiles, setImageFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [imagePreviews, setImagePreviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // History sidebar state\n    const [isHistoryCollapsed, setIsHistoryCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Edit message state\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingText, setEditingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoadingMessages, setIsLoadingMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Orchestration state\n    const [orchestrationExecutionId, setOrchestrationExecutionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrchestration, setShowOrchestration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orchestrationComplete, setOrchestrationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Canvas state for split-screen layout\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCanvasMinimized, setIsCanvasMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [triggerMaximize, setTriggerMaximize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle canvas state changes\n    const handleCanvasStateChange = (canvasOpen, canvasMinimized)=>{\n        setIsCanvasOpen(canvasOpen);\n        setIsCanvasMinimized(canvasMinimized);\n        // Auto-minimize history sidebar when canvas opens\n        if (canvasOpen && !canvasMinimized) {\n            setIsHistoryCollapsed(true);\n        }\n    };\n    // Disable sidebar hover when canvas is open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setHoverDisabled(isCanvasOpen && !isCanvasMinimized);\n    }, [\n        isCanvasOpen,\n        isCanvasMinimized,\n        setHoverDisabled\n    ]);\n    // Debug logs for orchestration state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDFAD [DEBUG] Orchestration state changed:\", {\n            showOrchestration,\n            orchestrationExecutionId,\n            isCanvasOpen,\n            isCanvasMinimized,\n            shouldRenderCanvas: showOrchestration && orchestrationExecutionId,\n            timestamp: new Date().toISOString()\n        });\n        if (showOrchestration && orchestrationExecutionId) {\n            console.log(\"\\uD83C\\uDFAD [DEBUG] Canvas should be visible now!\");\n        }\n    }, [\n        showOrchestration,\n        orchestrationExecutionId,\n        isCanvasOpen,\n        isCanvasMinimized\n    ]);\n    // Enhanced status tracking\n    const messageStatus = (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_10__.useSmartMessageStatus)({\n        enableAutoProgression: true,\n        onStageChange: (stage, timestamp)=>{\n            console.log(\"\\uD83C\\uDFAF Status: \".concat(stage, \" at \").concat(timestamp));\n        }\n    });\n    // Orchestration status tracking\n    const [orchestrationStatus, setOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Function to update orchestration status based on streaming content\n    const updateOrchestrationStatus = (deltaContent, messageStatusObj)=>{\n        let newStatus = \"\";\n        if (deltaContent.includes(\"\\uD83C\\uDFAC **Multi-Role AI Orchestration Started!**\")) {\n            newStatus = \"Multi-Role AI Orchestration Started\";\n        } else if (deltaContent.includes(\"\\uD83D\\uDCCB **Orchestration Plan:**\")) {\n            newStatus = \"Planning specialist assignments\";\n        } else if (deltaContent.includes(\"\\uD83E\\uDD16 **Moderator:**\")) {\n            newStatus = \"Moderator coordinating specialists\";\n        } else if (deltaContent.includes(\"Specialist:\") && deltaContent.includes(\"Working...\")) {\n            // Extract specialist name\n            const specialistMatch = deltaContent.match(/(\\w+)\\s+Specialist:/);\n            if (specialistMatch) {\n                newStatus = \"\".concat(specialistMatch[1], \" Specialist working\");\n            } else {\n                newStatus = \"Specialist working on your request\";\n            }\n        } else if (deltaContent.includes(\"\\uD83C\\uDFAD **SYNTHESIS PHASE INITIATED**\")) {\n            newStatus = \"Synthesizing specialist responses\";\n        } else if (deltaContent.includes(\"Analyzing and processing\")) {\n            newStatus = \"Analyzing and processing with specialized expertise\";\n        }\n        if (newStatus && newStatus !== orchestrationStatus) {\n            console.log(\"\\uD83C\\uDFAD Orchestration status update:\", newStatus);\n            setOrchestrationStatus(newStatus);\n            messageStatusObj.updateOrchestrationStatus(newStatus);\n        }\n    };\n    // Auto-continuation function for seamless multi-part responses\n    const handleAutoContinuation = async ()=>{\n        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Starting automatic continuation...\");\n        if (!selectedConfigId || !currentConversation) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Missing config or conversation\");\n            return;\n        }\n        setIsLoading(true);\n        setOrchestrationStatus(\"Continuing synthesis automatically...\");\n        messageStatus.startProcessing();\n        try {\n            // Create a continuation message\n            const continuationMessage = {\n                id: Date.now().toString() + \"-continue\",\n                role: \"user\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"continue\"\n                    }\n                ]\n            };\n            // Add the continuation message to the UI\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    continuationMessage\n                ]);\n            // Save continuation message to database\n            await saveMessageToDatabase(currentConversation.id, continuationMessage);\n            // Prepare payload for continuation\n            const continuationPayload = {\n                custom_api_config_id: selectedConfigId,\n                messages: [\n                    ...messages.map((m)=>({\n                            role: m.role,\n                            content: m.content.length === 1 && m.content[0].type === \"text\" ? m.content[0].text : m.content\n                        })),\n                    {\n                        role: \"user\",\n                        content: \"continue\"\n                    }\n                ],\n                stream: useStreaming\n            };\n            // Make the continuation request\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(continuationPayload),\n                cache: \"no-store\"\n            });\n            // Check for synthesis completion response\n            if (response.ok) {\n                // Check if this is a synthesis completion response\n                const responseText = await response.text();\n                let responseData;\n                try {\n                    responseData = JSON.parse(responseText);\n                } catch (e) {\n                    // If it's not JSON, treat as regular response\n                    responseData = null;\n                }\n                // Handle synthesis completion\n                if ((responseData === null || responseData === void 0 ? void 0 : responseData.error) === \"synthesis_complete\") {\n                    console.log('\\uD83C\\uDF89 [AUTO-CONTINUE] Synthesis is complete! Treating \"continue\" as new conversation.');\n                    // Remove the continuation message we just added\n                    setMessages((prevMessages)=>prevMessages.slice(0, -1));\n                    // Clear the loading state\n                    setIsLoading(false);\n                    setOrchestrationStatus(\"\");\n                    messageStatus.markComplete();\n                    // Process the \"continue\" as a new message by calling the normal send flow\n                    // But first we need to set the input back to \"continue\"\n                    setMessageInput(\"continue\");\n                    // Call the normal send message flow which will handle it as a new conversation\n                    setTimeout(()=>{\n                        handleSendMessage();\n                    }, 100);\n                    return;\n                }\n                // If not synthesis completion, recreate the response for normal processing\n                const recreatedResponse = new Response(responseText, {\n                    status: response.status,\n                    statusText: response.statusText,\n                    headers: response.headers\n                });\n                // Handle the continuation response\n                if (useStreaming && recreatedResponse.body) {\n                    const reader = recreatedResponse.body.getReader();\n                    const decoder = new TextDecoder();\n                    let assistantMessageId = Date.now().toString() + \"-assistant-continue\";\n                    let currentAssistantMessage = {\n                        id: assistantMessageId,\n                        role: \"assistant\",\n                        content: [\n                            {\n                                type: \"text\",\n                                text: \"\"\n                            }\n                        ]\n                    };\n                    setMessages((prevMessages)=>[\n                            ...prevMessages,\n                            currentAssistantMessage\n                        ]);\n                    let accumulatedText = \"\";\n                    let isOrchestrationDetected = false;\n                    let streamingStatusTimeout = null;\n                    // Check response headers to determine if this is chunked synthesis continuation\n                    const synthesisProgress = recreatedResponse.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = recreatedResponse.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    if (isChunkedSynthesis) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation\");\n                        messageStatus.markStreaming();\n                        setOrchestrationStatus(\"\");\n                    } else {\n                        // Start with continuation status, but allow orchestration detection to override\n                        messageStatus.markOrchestrationStarted();\n                        setOrchestrationStatus(\"Continuing synthesis...\");\n                        // Set up delayed streaming status, but allow orchestration detection to override\n                        streamingStatusTimeout = setTimeout(()=>{\n                            if (!isOrchestrationDetected) {\n                                console.log(\"\\uD83C\\uDFAF [AUTO-CONTINUE] No orchestration detected - switching to typing status\");\n                                messageStatus.markStreaming();\n                                setOrchestrationStatus(\"\");\n                            }\n                        }, 800);\n                    }\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const jsonData = line.substring(6);\n                                if (jsonData.trim() === \"[DONE]\") break;\n                                try {\n                                    var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                    const parsedChunk = JSON.parse(jsonData);\n                                    if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                        const deltaContent = parsedChunk.choices[0].delta.content;\n                                        accumulatedText += deltaContent;\n                                        // Skip old content-based orchestration detection - use headers only\n                                        const textContent = currentAssistantMessage.content[0];\n                                        textContent.text = accumulatedText;\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                    ...msg,\n                                                    content: [\n                                                        textContent\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Auto-continuation: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                                }\n                            }\n                        }\n                    }\n                    // Clean up timeout if still pending\n                    if (streamingStatusTimeout) {\n                        clearTimeout(streamingStatusTimeout);\n                    }\n                    // Save the continuation response\n                    if (accumulatedText) {\n                        const finalContinuationMessage = {\n                            ...currentAssistantMessage,\n                            content: [\n                                {\n                                    type: \"text\",\n                                    text: accumulatedText\n                                }\n                            ]\n                        };\n                        // Check if we need auto-continuation for chunked synthesis\n                        const needsAutoContinuation = isChunkedSynthesis && synthesisComplete !== \"true\" && accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\");\n                        if (needsAutoContinuation) {\n                            console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected chunked synthesis continuation, starting next chunk...\");\n                            // Save current message first\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                            // Start auto-continuation after a brief delay\n                            setTimeout(()=>{\n                                handleAutoContinuation();\n                            }, 1000);\n                        } else {\n                            await saveMessageToDatabase(currentConversation.id, finalContinuationMessage);\n                        }\n                    }\n                }\n            } else {\n                // Handle non-ok response\n                throw new Error(\"Auto-continuation failed: \".concat(response.status));\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Error:\", error);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-continue\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: \"Auto-continuation failed: \".concat(error instanceof Error ? error.message : \"Unknown error\")\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n            setOrchestrationStatus(\"\");\n            messageStatus.markComplete();\n        }\n    };\n    // Enhanced chat history with optimized caching\n    const { chatHistory, isLoading: isLoadingHistory, isStale: isChatHistoryStale, error: chatHistoryError, refetch: refetchChatHistory, prefetch: prefetchChatHistory, invalidateCache: invalidateChatHistoryCache } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_9__.useChatHistory)({\n        configId: selectedConfigId,\n        enablePrefetch: true,\n        cacheTimeout: 300000,\n        staleTimeout: 30000 // 30 seconds - show stale data while fetching fresh\n    });\n    // Chat history prefetching hook\n    const { prefetchChatHistory: prefetchForNavigation } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_9__.useChatHistoryPrefetch)();\n    // Conversation starters\n    const conversationStarters = [\n        {\n            id: \"write-copy\",\n            title: \"Write copy\",\n            description: \"Create compelling marketing content\",\n            icon: \"✍️\",\n            color: \"bg-amber-100 text-amber-700\",\n            prompt: \"Help me write compelling copy for my product landing page\"\n        },\n        {\n            id: \"image-generation\",\n            title: \"Image generation\",\n            description: \"Create visual content descriptions\",\n            icon: \"\\uD83C\\uDFA8\",\n            color: \"bg-blue-100 text-blue-700\",\n            prompt: \"Help me create detailed prompts for AI image generation\"\n        },\n        {\n            id: \"create-avatar\",\n            title: \"Create avatar\",\n            description: \"Design character personas\",\n            icon: \"\\uD83D\\uDC64\",\n            color: \"bg-green-100 text-green-700\",\n            prompt: \"Help me create a detailed character avatar for my story\"\n        },\n        {\n            id: \"write-code\",\n            title: \"Write code\",\n            description: \"Generate and debug code\",\n            icon: \"\\uD83D\\uDCBB\",\n            color: \"bg-purple-100 text-purple-700\",\n            prompt: \"Help me write clean, efficient code for my project\"\n        }\n    ];\n    // Fetch Custom API Configs for the dropdown with progressive loading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchConfigs = async ()=>{\n            try {\n                // Progressive loading: render UI first, then load configs\n                if (initialPageLoad) {\n                    await new Promise((resolve)=>setTimeout(resolve, 50));\n                }\n                const response = await fetch(\"/api/custom-configs\");\n                if (!response.ok) {\n                    const errData = await response.json();\n                    throw new Error(errData.error || \"Failed to fetch configurations\");\n                }\n                const data = await response.json();\n                setCustomConfigs(data);\n                if (data.length > 0) {\n                    setSelectedConfigId(data[0].id);\n                }\n                setInitialPageLoad(false);\n            } catch (err) {\n                setError(\"Failed to load configurations: \".concat(err.message));\n                setCustomConfigs([]);\n                setInitialPageLoad(false);\n            }\n        };\n        // Call immediately to ensure configs load properly\n        fetchConfigs();\n    }, [\n        initialPageLoad\n    ]);\n    // Helper function to convert File to base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    const handleImageChange = async (event)=>{\n        const files = Array.from(event.target.files || []);\n        if (files.length === 0) return;\n        // Limit to 10 images total\n        const currentCount = imageFiles.length;\n        const availableSlots = 10 - currentCount;\n        const filesToAdd = files.slice(0, availableSlots);\n        if (filesToAdd.length < files.length) {\n            setError(\"You can only upload up to 10 images. \".concat(files.length - filesToAdd.length, \" images were not added.\"));\n        }\n        try {\n            const newPreviews = [];\n            for (const file of filesToAdd){\n                const previewUrl = await fileToBase64(file);\n                newPreviews.push(previewUrl);\n            }\n            setImageFiles((prev)=>[\n                    ...prev,\n                    ...filesToAdd\n                ]);\n            setImagePreviews((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error processing images:\", error);\n            setError(\"Failed to process one or more images. Please try again.\");\n        }\n        // Reset file input\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleRemoveImage = (index)=>{\n        if (index !== undefined) {\n            // Remove specific image\n            setImageFiles((prev)=>prev.filter((_, i)=>i !== index));\n            setImagePreviews((prev)=>prev.filter((_, i)=>i !== index));\n        } else {\n            // Remove all images\n            setImageFiles([]);\n            setImagePreviews([]);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\"; // Reset file input\n        }\n    };\n    // Scroll management functions\n    const scrollToBottom = function() {\n        let smooth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (messagesContainerRef.current) {\n            messagesContainerRef.current.scrollTo({\n                top: messagesContainerRef.current.scrollHeight,\n                behavior: smooth ? \"smooth\" : \"auto\"\n            });\n        }\n    };\n    const handleScroll = (e)=>{\n        const container = e.currentTarget;\n        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n        setShowScrollToBottom(!isNearBottom && messages.length > 0);\n    };\n    // Auto-scroll to bottom when new messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messages.length > 0) {\n            // Use requestAnimationFrame to ensure DOM has updated\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages.length\n    ]);\n    // Auto-scroll during streaming responses\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            // Scroll to bottom during streaming to show new content\n            requestAnimationFrame(()=>{\n                scrollToBottom();\n            });\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Auto-scroll when streaming content updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && messages.length > 0) {\n            const lastMessage = messages[messages.length - 1];\n            if (lastMessage && lastMessage.role === \"assistant\") {\n                // Scroll to bottom when assistant message content updates during streaming\n                requestAnimationFrame(()=>{\n                    scrollToBottom();\n                });\n            }\n        }\n    }, [\n        messages,\n        isLoading\n    ]);\n    // Handle sidebar state changes to ensure proper centering\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Small delay to allow CSS transitions to complete\n        const timer = setTimeout(()=>{\n            if (messages.length > 0) {\n                // Maintain scroll position when sidebar toggles\n                requestAnimationFrame(()=>{\n                    if (messagesContainerRef.current) {\n                        const container = messagesContainerRef.current;\n                        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;\n                        if (isNearBottom) {\n                            scrollToBottom();\n                        }\n                    }\n                });\n            }\n        }, 200); // Match the transition duration\n        return ()=>clearTimeout(timer);\n    }, [\n        isCollapsed,\n        isHovered,\n        isHistoryCollapsed,\n        messages.length\n    ]);\n    // Prefetch chat history when hovering over configs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedConfigId && customConfigs.length > 0) {\n            // Prefetch chat history for other configs when user is idle\n            const otherConfigs = customConfigs.filter((config)=>config.id !== selectedConfigId).slice(0, 3); // Limit to 3 most recent other configs\n            const timer = setTimeout(()=>{\n                otherConfigs.forEach((config)=>{\n                    prefetchForNavigation(config.id);\n                });\n            }, 2000); // Wait 2 seconds before prefetching\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        selectedConfigId,\n        customConfigs,\n        prefetchForNavigation\n    ]);\n    // Load messages for a specific conversation with pagination\n    const loadConversation = async function(conversation) {\n        let loadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // Set loading state for message loading\n        if (!loadMore) {\n            setIsLoadingMessages(true);\n        }\n        try {\n            // Note: isLoadingHistory is now managed by the useChatHistory hook\n            // For initial load, get latest 50 messages\n            // For load more, get older messages with offset\n            const limit = 50;\n            const offset = loadMore ? messages.length : 0;\n            const latest = !loadMore;\n            // Add cache-busting parameter to ensure fresh data after edits\n            const cacheBuster = Date.now();\n            const response = await fetch(\"/api/chat/messages?conversation_id=\".concat(conversation.id, \"&limit=\").concat(limit, \"&offset=\").concat(offset, \"&latest=\").concat(latest, \"&_cb=\").concat(cacheBuster), {\n                cache: \"no-store\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load conversation messages\");\n            }\n            const chatMessages = await response.json();\n            // Convert ChatMessage to PlaygroundMessage format\n            const playgroundMessages = chatMessages.map((msg)=>({\n                    id: msg.id,\n                    role: msg.role,\n                    content: msg.content.map((part)=>{\n                        var _part_image_url;\n                        if (part.type === \"text\" && part.text) {\n                            return {\n                                type: \"text\",\n                                text: part.text\n                            };\n                        } else if (part.type === \"image_url\" && ((_part_image_url = part.image_url) === null || _part_image_url === void 0 ? void 0 : _part_image_url.url)) {\n                            return {\n                                type: \"image_url\",\n                                image_url: {\n                                    url: part.image_url.url\n                                }\n                            };\n                        } else {\n                            // Fallback for malformed content\n                            return {\n                                type: \"text\",\n                                text: \"\"\n                            };\n                        }\n                    })\n                }));\n            if (loadMore) {\n                // Prepend older messages to the beginning\n                setMessages((prev)=>[\n                        ...playgroundMessages,\n                        ...prev\n                    ]);\n            } else {\n                // Replace all messages for initial load\n                setMessages(playgroundMessages);\n                // Note: currentConversation is now set optimistically in loadChatFromHistory\n                // Only set it here if it's not already set (for direct loadConversation calls)\n                if (!currentConversation || currentConversation.id !== conversation.id) {\n                    setCurrentConversation(conversation);\n                }\n            }\n            setError(null);\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        } finally{\n            // Clear loading state for message loading\n            if (!loadMore) {\n                setIsLoadingMessages(false);\n            }\n        // Note: isLoadingHistory is now managed by the useChatHistory hook\n        }\n    };\n    // Save current conversation\n    const saveConversation = async ()=>{\n        if (!selectedConfigId || messages.length === 0) return null;\n        try {\n            let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const firstMessage = messages[0];\n                let title = \"New Chat\";\n                if (firstMessage && firstMessage.content.length > 0) {\n                    const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                    if (textPart && textPart.text) {\n                        title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                    }\n                }\n                const newConversationData = {\n                    custom_api_config_id: selectedConfigId,\n                    title\n                };\n                const response = await fetch(\"/api/chat/conversations\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newConversationData)\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create conversation\");\n                }\n                const newConversation = await response.json();\n                conversationId = newConversation.id;\n                setCurrentConversation(newConversation);\n            }\n            // Save all messages that aren't already saved\n            for (const message of messages){\n                // Check if message is already saved (has UUID format)\n                if (message.id.includes(\"-\") && message.id.length > 20) continue;\n                const newMessageData = {\n                    conversation_id: conversationId,\n                    role: message.role,\n                    content: message.content\n                };\n                await fetch(\"/api/chat/messages\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(newMessageData)\n                });\n            }\n            // Only refresh chat history if we created a new conversation\n            if (!currentConversation) {\n                refetchChatHistory(true); // Force refresh for new conversations\n            }\n            return conversationId;\n        } catch (err) {\n            console.error(\"Error saving conversation:\", err);\n            setError(\"Failed to save conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Delete a conversation\n    const deleteConversation = async (conversationId)=>{\n        try {\n            const response = await fetch(\"/api/chat/conversations?id=\".concat(conversationId), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete conversation\");\n            }\n            // If this was the current conversation, clear it\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === conversationId) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n            // Force refresh chat history after deletion\n            refetchChatHistory(true);\n        } catch (err) {\n            console.error(\"Error deleting conversation:\", err);\n            setError(\"Failed to delete conversation: \".concat(err.message));\n        }\n    };\n    // Create a new conversation automatically when first message is sent\n    const createNewConversation = async (firstMessage)=>{\n        if (!selectedConfigId) return null;\n        try {\n            // Generate title from first message\n            let title = \"New Chat\";\n            if (firstMessage.content.length > 0) {\n                const textPart = firstMessage.content.find((part)=>part.type === \"text\");\n                if (textPart && textPart.text) {\n                    title = textPart.text.slice(0, 50) + (textPart.text.length > 50 ? \"...\" : \"\");\n                }\n            }\n            const newConversationData = {\n                custom_api_config_id: selectedConfigId,\n                title\n            };\n            const response = await fetch(\"/api/chat/conversations\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newConversationData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to create conversation\");\n            }\n            const newConversation = await response.json();\n            setCurrentConversation(newConversation);\n            return newConversation.id;\n        } catch (err) {\n            console.error(\"Error creating conversation:\", err);\n            setError(\"Failed to create conversation: \".concat(err.message));\n            return null;\n        }\n    };\n    // Save individual message to database\n    const saveMessageToDatabase = async (conversationId, message)=>{\n        try {\n            const newMessageData = {\n                conversation_id: conversationId,\n                role: message.role,\n                content: message.content\n            };\n            const response = await fetch(\"/api/chat/messages\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newMessageData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save message\");\n            }\n            return await response.json();\n        } catch (err) {\n            console.error(\"Error saving message:\", err);\n        // Don't show error to user for message saving failures\n        // The conversation will still work in the UI\n        }\n    };\n    const handleStarterClick = (prompt)=>{\n        setMessageInput(prompt);\n        // Auto-focus the input after setting the prompt\n        setTimeout(()=>{\n            const textarea = document.querySelector('textarea[placeholder*=\"Type a message\"]');\n            if (textarea) {\n                textarea.focus();\n                textarea.setSelectionRange(textarea.value.length, textarea.value.length);\n            }\n        }, 100);\n    };\n    const startNewChat = async ()=>{\n        // Save current conversation if it has messages\n        if (messages.length > 0) {\n            await saveConversation();\n        }\n        setMessages([]);\n        setCurrentConversation(null);\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Reset status tracking\n        messageStatus.reset();\n    };\n    // Handle model/router configuration change\n    const handleConfigChange = async (newConfigId)=>{\n        // Don't do anything if it's the same config\n        if (newConfigId === selectedConfigId) return;\n        // If there's an existing conversation with messages, start a new chat\n        if (messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [Model Switch] Starting new chat due to model change\");\n            await startNewChat();\n        }\n        // Update the selected configuration\n        setSelectedConfigId(newConfigId);\n        // Find the config name for logging\n        const selectedConfig = customConfigs.find((config)=>config.id === newConfigId);\n        const configName = selectedConfig ? selectedConfig.name : newConfigId;\n        console.log(\"\\uD83D\\uDD04 [Model Switch] Switched to config: \".concat(configName, \" (\").concat(newConfigId, \")\"));\n    };\n    const loadChatFromHistory = async (conversation)=>{\n        // Optimistic UI update - immediately switch to the selected conversation\n        console.log(\"\\uD83D\\uDD04 [INSTANT SWITCH] Immediately switching to conversation: \".concat(conversation.title));\n        // Clear current state immediately for instant feedback\n        setCurrentConversation(conversation);\n        setMessages([]); // Clear messages immediately to show loading state\n        setMessageInput(\"\");\n        setError(null);\n        handleRemoveImage();\n        // Save current conversation in background (non-blocking)\n        const savePromise = (async ()=>{\n            if (messages.length > 0 && !currentConversation) {\n                try {\n                    await saveConversation();\n                } catch (err) {\n                    console.error(\"Background save failed:\", err);\n                }\n            }\n        })();\n        // Load conversation messages in background\n        try {\n            await loadConversation(conversation);\n            console.log(\"✅ [INSTANT SWITCH] Successfully loaded conversation: \".concat(conversation.title));\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(\"Failed to load conversation: \".concat(err.message));\n        // Don't revert currentConversation - keep the UI showing the selected conversation\n        }\n        // Ensure background save completes\n        await savePromise;\n    };\n    // Edit message functionality\n    const startEditingMessage = (messageId, currentText)=>{\n        setEditingMessageId(messageId);\n        setEditingText(currentText);\n    };\n    const cancelEditingMessage = ()=>{\n        setEditingMessageId(null);\n        setEditingText(\"\");\n    };\n    const saveEditedMessage = async ()=>{\n        if (!editingMessageId || !editingText.trim() || !selectedConfigId) return;\n        // Find the index of the message being edited\n        const messageIndex = messages.findIndex((msg)=>msg.id === editingMessageId);\n        if (messageIndex === -1) return;\n        // Update the message content\n        const updatedMessages = [\n            ...messages\n        ];\n        updatedMessages[messageIndex] = {\n            ...updatedMessages[messageIndex],\n            content: [\n                {\n                    type: \"text\",\n                    text: editingText.trim()\n                }\n            ]\n        };\n        // Remove all messages after the edited message (restart conversation from this point)\n        const messagesToKeep = updatedMessages.slice(0, messageIndex + 1);\n        setMessages(messagesToKeep);\n        setEditingMessageId(null);\n        setEditingText(\"\");\n        // If we have a current conversation, update the database\n        if (currentConversation) {\n            try {\n                // Delete messages after the edited one from the database\n                const messagesToDelete = messages.slice(messageIndex + 1);\n                console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting \".concat(messagesToDelete.length, \" messages after edited message\"));\n                // Instead of trying to identify saved messages by ID format,\n                // delete all messages after the edited message's timestamp from the database\n                if (messagesToDelete.length > 0) {\n                    const editedMessage = messages[messageIndex];\n                    const editedMessageTimestamp = parseInt(editedMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [EDIT MODE] Deleting all messages after timestamp: \".concat(editedMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            after_timestamp: editedMessageTimestamp\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages after timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [EDIT MODE] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Update/save the edited message in the database\n                const editedMessage = messagesToKeep[messageIndex];\n                console.log(\"✏️ [EDIT MODE] Saving edited message with timestamp: \".concat(editedMessage.id));\n                // Use timestamp-based update to find and update the message\n                const updateResponse = await fetch(\"/api/chat/messages/update-by-timestamp\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        conversation_id: currentConversation.id,\n                        timestamp: parseInt(editedMessage.id),\n                        content: editedMessage.content\n                    })\n                });\n                if (!updateResponse.ok) {\n                    console.error(\"Failed to update message by timestamp:\", await updateResponse.text());\n                    // If update fails, try to save as new message (fallback)\n                    console.log(\"\\uD83D\\uDCDD [EDIT MODE] Fallback: Saving edited message as new message\");\n                    await saveMessageToDatabase(currentConversation.id, editedMessage);\n                } else {\n                    const result = await updateResponse.json();\n                    console.log(\"✅ [EDIT MODE] Successfully updated message: \".concat(result.message));\n                }\n                // Force refresh chat history to reflect changes and clear cache\n                refetchChatHistory(true);\n                // Also clear any message cache by adding a cache-busting parameter\n                if (true) {\n                    // Clear any cached conversation data\n                    const cacheKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"chat_\") || key.startsWith(\"conversation_\"));\n                    cacheKeys.forEach((key)=>localStorage.removeItem(key));\n                }\n            } catch (err) {\n                console.error(\"Error updating conversation:\", err);\n                setError(\"Failed to update conversation: \".concat(err.message));\n            }\n        }\n        // Now automatically send the edited message to get a response\n        await sendEditedMessageToAPI(messagesToKeep);\n    };\n    // Send the edited conversation to get a new response\n    const sendEditedMessageToAPI = async (conversationMessages)=>{\n        if (!selectedConfigId || conversationMessages.length === 0) return;\n        setIsLoading(true);\n        setError(null);\n        // Start status tracking for edit mode\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Sending edited conversation for new response...\");\n        // Prepare payload with the conversation up to the edited message\n        const messagesForPayload = conversationMessages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming\n        };\n        try {\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [EDIT MODE] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            if (useStreaming && response.body) {\n                // Handle streaming response with orchestration detection (same as handleSendMessage)\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF [EDIT MODE] Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Skip old content-based orchestration detection - use headers only\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Failed to parse stream chunk:\", parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                // Save the assistant response with auto-continuation support\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [EDIT MODE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                        // Start auto-continuation after a brief delay\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, 2000);\n                    } else {\n                        await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                    }\n                }\n            } else {\n                var _data_choices__message, _data_choices_, _data_choices, _data_content_, _data_content;\n                // Handle non-streaming response\n                const data = await response.json();\n                let assistantContent = \"Could not parse assistant's response.\";\n                if ((_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content) {\n                    assistantContent = data.choices[0].message.content;\n                } else if ((_data_content = data.content) === null || _data_content === void 0 ? void 0 : (_data_content_ = _data_content[0]) === null || _data_content_ === void 0 ? void 0 : _data_content_.text) {\n                    assistantContent = data.content[0].text;\n                } else if (typeof data.text === \"string\") {\n                    assistantContent = data.text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save the assistant response\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Edit mode API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [EDIT MODE] Processing complete\");\n        }\n    };\n    // Handle retry message with optional specific API key\n    const handleRetryMessage = async (messageIndex, apiKeyId)=>{\n        if (!selectedConfigId || messageIndex < 0 || messageIndex >= messages.length) return;\n        const messageToRetry = messages[messageIndex];\n        if (messageToRetry.role !== \"assistant\") return;\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start status tracking for retry\n        messageStatus.startProcessing();\n        console.log(\"\\uD83D\\uDD04 [RETRY] Retrying message with\", apiKeyId ? \"specific key: \".concat(apiKeyId) : \"same model\");\n        // Remove the assistant message and any messages after it\n        const messagesToKeep = messages.slice(0, messageIndex);\n        setMessages(messagesToKeep);\n        // If we have a current conversation, delete the retried message and subsequent ones from database\n        if (currentConversation) {\n            try {\n                const messagesToDelete = messages.slice(messageIndex);\n                console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting \".concat(messagesToDelete.length, \" messages from retry point\"));\n                // Delete all messages from the retry point onwards using timestamp-based deletion\n                if (messagesToDelete.length > 0) {\n                    const retryMessage = messages[messageIndex];\n                    const retryMessageTimestamp = parseInt(retryMessage.id) || Date.now();\n                    console.log(\"\\uD83D\\uDDD1️ [RETRY] Deleting all messages from timestamp: \".concat(retryMessageTimestamp));\n                    const deleteResponse = await fetch(\"/api/chat/messages/delete-after-timestamp\", {\n                        method: \"DELETE\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            conversation_id: currentConversation.id,\n                            from_timestamp: retryMessageTimestamp // Use 'from' instead of 'after' to include the retry message\n                        })\n                    });\n                    if (!deleteResponse.ok) {\n                        console.error(\"Failed to delete messages from timestamp:\", await deleteResponse.text());\n                    } else {\n                        const result = await deleteResponse.json();\n                        console.log(\"✅ [RETRY] Successfully deleted \".concat(result.deleted_count, \" messages\"));\n                    }\n                }\n                // Refresh chat history to reflect changes\n                refetchChatHistory(true);\n            } catch (err) {\n                console.error(\"Error deleting retried messages:\", err);\n            }\n        }\n        // Prepare payload with messages up to the retry point\n        const messagesForPayload = messagesToKeep.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\";\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                contentForApi = m.content[0].text;\n            } else {\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: messagesForPayload,\n            stream: useStreaming,\n            ...apiKeyId && {\n                specific_api_key_id: apiKeyId\n            } // Add specific key if provided\n        };\n        try {\n            console.log(\"\\uD83D\\uDE80 [RETRY] Starting retry API call...\");\n            // Update status to connecting\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [RETRY] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Check for orchestration headers in retry\n            const orchestrationId = response.headers.get(\"X-RoKey-Orchestration-ID\");\n            const orchestrationActive = response.headers.get(\"X-RoKey-Orchestration-Active\");\n            if (orchestrationId && orchestrationActive === \"true\") {\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration headers in retry - showing canvas IMMEDIATELY\");\n                setOrchestrationExecutionId(orchestrationId);\n                setShowOrchestration(true);\n                setOrchestrationComplete(false);\n                // Force canvas to be visible and not minimized\n                setIsCanvasOpen(true);\n                setIsCanvasMinimized(false);\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Canvas should be opening now (retry)!\");\n            }\n            // Brief delay to show the backend process, then switch to streaming\n            setTimeout(()=>{\n                if (useStreaming) {\n                    console.log(\"\\uD83C\\uDFAF [RETRY] Response OK - switching to typing status\");\n                    messageStatus.markStreaming();\n                }\n            }, 400); // Give time to show the backend process stage\n            // Handle streaming or non-streaming response (reuse existing logic)\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let accumulatedText = \"\";\n                const currentAssistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split(\"\\n\");\n                        for (const line of lines){\n                            if (line.startsWith(\"data: \")) {\n                                const data = line.slice(6);\n                                if (data === \"[DONE]\") continue;\n                                try {\n                                    var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                    const parsed = JSON.parse(data);\n                                    if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                        const newContent = parsed.choices[0].delta.content;\n                                        accumulatedText += newContent;\n                                        // Skip old content-based orchestration detection - use headers only\n                                        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === currentAssistantMessage.id ? {\n                                                    ...msg,\n                                                    content: [\n                                                        {\n                                                            type: \"text\",\n                                                            text: accumulatedText\n                                                        }\n                                                    ]\n                                                } : msg));\n                                    }\n                                } catch (parseError) {\n                                    console.warn(\"Failed to parse streaming chunk:\", parseError);\n                                }\n                            }\n                        }\n                    }\n                } finally{\n                    reader.releaseLock();\n                }\n                // Save final assistant message\n                if (accumulatedText && currentConversation) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    await saveMessageToDatabase(currentConversation.id, finalAssistantMessage);\n                }\n            } else {\n                // Non-streaming response\n                const data = await response.json();\n                let assistantContent = \"\";\n                if (data.choices && data.choices.length > 0 && data.choices[0].message) {\n                    assistantContent = data.choices[0].message.content;\n                } else if (data.content && Array.isArray(data.content) && data.content.length > 0) {\n                    assistantContent = data.content[0].text;\n                }\n                const assistantMessage = {\n                    id: Date.now().toString() + \"-assistant-retry\",\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: assistantContent\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        assistantMessage\n                    ]);\n                // Save assistant message\n                if (currentConversation) {\n                    await saveMessageToDatabase(currentConversation.id, assistantMessage);\n                }\n            }\n        } catch (err) {\n            console.error(\"Retry API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error-retry\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred during retry.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Save error message\n            if (currentConversation) {\n                await saveMessageToDatabase(currentConversation.id, errorMessage);\n            }\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            console.log(\"\\uD83C\\uDFAF [RETRY] Processing complete\");\n        }\n    };\n    const handleSendMessage = async (e)=>{\n        if (e) e.preventDefault();\n        // Allow sending if there's text OR images\n        if (!messageInput.trim() && imageFiles.length === 0 || !selectedConfigId) return;\n        // Check if this is a continuation request\n        const inputText = messageInput.trim().toLowerCase();\n        if (inputText === \"continue\" && messages.length > 0) {\n            console.log(\"\\uD83D\\uDD04 [CONTINUE] Detected manual continuation request, routing to auto-continuation...\");\n            // Clear the input\n            setMessageInput(\"\");\n            // Route to auto-continuation instead of normal message flow\n            await handleAutoContinuation();\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        // Reset orchestration status\n        setOrchestrationStatus(\"\");\n        // Start enhanced status tracking\n        messageStatus.startProcessing();\n        // Phase 1 Optimization: Performance tracking\n        const messagingStartTime = performance.now();\n        console.log(\"\\uD83D\\uDE80 [MESSAGING FLOW] Starting optimized parallel processing...\");\n        // Capture current input and images before clearing them\n        const currentMessageInput = messageInput.trim();\n        const currentImageFiles = [\n            ...imageFiles\n        ];\n        const currentImagePreviews = [\n            ...imagePreviews\n        ];\n        // Clear input and images immediately to prevent them from showing after send\n        setMessageInput(\"\");\n        handleRemoveImage();\n        const userMessageContentParts = [];\n        let apiMessageContentParts = []; // For the API payload, image_url.url will be base64\n        if (currentMessageInput) {\n            userMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n            apiMessageContentParts.push({\n                type: \"text\",\n                text: currentMessageInput\n            });\n        }\n        // Process all images\n        if (currentImageFiles.length > 0) {\n            try {\n                for(let i = 0; i < currentImageFiles.length; i++){\n                    const file = currentImageFiles[i];\n                    const preview = currentImagePreviews[i];\n                    const base64ImageData = await fileToBase64(file);\n                    // For UI display (uses the preview which is already base64)\n                    userMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: preview\n                        }\n                    });\n                    // For API payload\n                    apiMessageContentParts.push({\n                        type: \"image_url\",\n                        image_url: {\n                            url: base64ImageData\n                        }\n                    });\n                }\n            } catch (imgErr) {\n                console.error(\"Error converting images to base64:\", imgErr);\n                setError(\"Failed to process one or more images. Please try again.\");\n                setIsLoading(false);\n                // Restore the input and images if there was an error\n                setMessageInput(currentMessageInput);\n                setImageFiles(currentImageFiles);\n                setImagePreviews(currentImagePreviews);\n                return;\n            }\n        }\n        const newUserMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: userMessageContentParts\n        };\n        setMessages((prevMessages)=>[\n                ...prevMessages,\n                newUserMessage\n            ]);\n        // Phase 1 Optimization: Start conversation creation and user message saving in background\n        // Don't wait for these operations - they can happen in parallel with LLM call\n        let conversationId = currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id;\n        let conversationPromise = Promise.resolve(conversationId);\n        let userMessageSavePromise = Promise.resolve();\n        if (!conversationId && !currentConversation) {\n            console.log(\"\\uD83D\\uDD04 [PARALLEL] Starting conversation creation in background...\");\n            conversationPromise = createNewConversation(newUserMessage);\n        }\n        // Start user message saving in background (will wait for conversation if needed)\n        userMessageSavePromise = conversationPromise.then(async (convId)=>{\n            if (convId) {\n                console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving user message in background...\");\n                await saveMessageToDatabase(convId, newUserMessage);\n                console.log(\"✅ [PARALLEL] User message saved\");\n                return convId;\n            }\n        }).catch((err)=>{\n            console.error(\"❌ [PARALLEL] User message save failed:\", err);\n        });\n        // Prepare payload.messages by transforming existing messages and adding the new one\n        const existingMessagesForPayload = messages.filter((m)=>m.role === \"user\" || m.role === \"assistant\" || m.role === \"system\").map((m)=>{\n            let contentForApi;\n            if (m.role === \"system\") {\n                // System messages are always simple text strings\n                // Their content in PlaygroundMessage is [{type: 'text', text: 'Actual system prompt'}]\n                const firstPart = m.content[0];\n                if (firstPart && firstPart.type === \"text\") {\n                    contentForApi = firstPart.text;\n                } else {\n                    contentForApi = \"\"; // Fallback, though system messages should always be text\n                }\n            } else if (m.content.length === 1 && m.content[0].type === \"text\") {\n                // Single text part for user/assistant, send as string for API\n                contentForApi = m.content[0].text;\n            } else {\n                // Multimodal content (e.g., user message with image) or multiple parts\n                contentForApi = m.content.map((part)=>{\n                    if (part.type === \"image_url\") {\n                        // The part.image_url.url from messages state is the base64 data URL (preview)\n                        // This is what we want to send to the backend.\n                        return {\n                            type: \"image_url\",\n                            image_url: {\n                                url: part.image_url.url\n                            }\n                        };\n                    }\n                    // Ensure it's properly cast for text part before accessing .text\n                    return {\n                        type: \"text\",\n                        text: part.text\n                    };\n                });\n            }\n            return {\n                role: m.role,\n                content: contentForApi\n            };\n        });\n        const payload = {\n            custom_api_config_id: selectedConfigId,\n            messages: [\n                ...existingMessagesForPayload,\n                {\n                    role: \"user\",\n                    content: apiMessageContentParts.length === 1 && apiMessageContentParts[0].type === \"text\" ? apiMessageContentParts[0].text : apiMessageContentParts\n                }\n            ],\n            stream: useStreaming\n        };\n        try {\n            // Phase 1 Optimization: Start LLM call immediately in parallel with background operations\n            console.log(\"\\uD83D\\uDE80 [PARALLEL] Starting LLM API call...\");\n            messageStatus.updateStage(\"connecting\");\n            const llmStartTime = performance.now();\n            const response = await fetch(\"/api/v1/chat/completions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(\"Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13\" || 0)\n                },\n                body: JSON.stringify(payload),\n                // Conservative performance optimizations\n                cache: \"no-store\"\n            });\n            const llmResponseTime = performance.now() - llmStartTime;\n            console.log(\"⚡ [PARALLEL] LLM API response received in \".concat(llmResponseTime.toFixed(1), \"ms\"));\n            if (!response.ok) {\n                const errData = await response.json();\n                throw new Error(errData.error || \"API Error: \".concat(response.statusText, \" (Status: \").concat(response.status, \")\"));\n            }\n            // Analyze response headers to show what backend processes actually ran\n            messageStatus.analyzeResponseHeaders(response.headers);\n            // Check for orchestration headers\n            const orchestrationId = response.headers.get(\"X-RoKey-Orchestration-ID\");\n            const orchestrationActive = response.headers.get(\"X-RoKey-Orchestration-Active\");\n            console.log(\"\\uD83C\\uDFAD [DEBUG] Checking orchestration headers:\", {\n                orchestrationId,\n                orchestrationActive,\n                allHeaders: Object.fromEntries(response.headers.entries())\n            });\n            if (orchestrationId && orchestrationActive === \"true\") {\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Detected orchestration headers - showing canvas IMMEDIATELY\");\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Orchestration ID:\", orchestrationId);\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Stream URL will be:\", \"/api/orchestration/stream/\".concat(orchestrationId));\n                // Force canvas to open immediately when headers are detected\n                setOrchestrationExecutionId(orchestrationId);\n                setShowOrchestration(true);\n                setOrchestrationComplete(false);\n                // Force canvas to be visible and not minimized\n                setIsCanvasOpen(true);\n                setIsCanvasMinimized(false);\n                console.log(\"\\uD83C\\uDFAD [ORCHESTRATION] Canvas should be opening now!\");\n            } else {\n                console.log(\"\\uD83C\\uDFAD [DEBUG] No orchestration headers found or not active\");\n                console.log(\"\\uD83C\\uDFAD [DEBUG] orchestrationId:\", orchestrationId);\n                console.log(\"\\uD83C\\uDFAD [DEBUG] orchestrationActive:\", orchestrationActive);\n            }\n            // If we're here, it's a stream.\n            if (useStreaming && response.body) {\n                const reader = response.body.getReader();\n                const decoder = new TextDecoder();\n                let assistantMessageId = Date.now().toString() + \"-assistant\";\n                let currentAssistantMessage = {\n                    id: assistantMessageId,\n                    role: \"assistant\",\n                    content: [\n                        {\n                            type: \"text\",\n                            text: \"\"\n                        }\n                    ]\n                };\n                setMessages((prevMessages)=>[\n                        ...prevMessages,\n                        currentAssistantMessage\n                    ]);\n                let accumulatedText = \"\";\n                let isOrchestrationDetected = false;\n                let streamingStatusTimeout = null;\n                // Set up delayed streaming status, but allow orchestration detection to override\n                streamingStatusTimeout = setTimeout(()=>{\n                    if (!isOrchestrationDetected) {\n                        console.log(\"\\uD83C\\uDFAF Response OK - switching to typing status (no orchestration detected)\");\n                        messageStatus.markStreaming();\n                    }\n                }, 400);\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = chunk.split(\"\\n\");\n                    for (const line of lines){\n                        if (line.startsWith(\"data: \")) {\n                            const jsonData = line.substring(6);\n                            if (jsonData.trim() === \"[DONE]\") break;\n                            try {\n                                var _parsedChunk_choices__delta, _parsedChunk_choices_;\n                                const parsedChunk = JSON.parse(jsonData);\n                                if (parsedChunk.choices && ((_parsedChunk_choices_ = parsedChunk.choices[0]) === null || _parsedChunk_choices_ === void 0 ? void 0 : (_parsedChunk_choices__delta = _parsedChunk_choices_.delta) === null || _parsedChunk_choices__delta === void 0 ? void 0 : _parsedChunk_choices__delta.content)) {\n                                    const deltaContent = parsedChunk.choices[0].delta.content;\n                                    accumulatedText += deltaContent;\n                                    // Skip old content-based orchestration detection - use headers only\n                                    // Update orchestration progress based on content\n                                    if (isOrchestrationDetected) {\n                                        updateOrchestrationStatus(deltaContent, messageStatus);\n                                    }\n                                    const textContent = currentAssistantMessage.content[0];\n                                    textContent.text = accumulatedText;\n                                    setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === assistantMessageId ? {\n                                                ...msg,\n                                                content: [\n                                                    textContent\n                                                ]\n                                            } : msg));\n                                }\n                            } catch (parseError) {\n                                console.warn(\"Playground: Failed to parse stream chunk JSON:\", jsonData, parseError);\n                            }\n                        }\n                    }\n                }\n                // Clean up timeout if still pending\n                if (streamingStatusTimeout) {\n                    clearTimeout(streamingStatusTimeout);\n                }\n                if (accumulatedText) {\n                    const finalAssistantMessage = {\n                        ...currentAssistantMessage,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: accumulatedText\n                            }\n                        ]\n                    };\n                    // Check response headers to determine if this is chunked synthesis\n                    const synthesisProgress = response.headers.get(\"X-Synthesis-Progress\");\n                    const synthesisComplete = response.headers.get(\"X-Synthesis-Complete\");\n                    const isChunkedSynthesis = synthesisProgress !== null;\n                    // Check if we need auto-continuation\n                    const needsAutoContinuation = accumulatedText.includes(\"[SYNTHESIS CONTINUES AUTOMATICALLY...]\") || accumulatedText.includes(\"*The response will continue automatically in a new message...*\");\n                    if (needsAutoContinuation) {\n                        console.log(\"\\uD83D\\uDD04 [AUTO-CONTINUE] Detected auto-continuation marker, starting new response...\");\n                        // Save current message first\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                        // For chunked synthesis, start continuation immediately\n                        // For regular synthesis, add a delay\n                        const delay = isChunkedSynthesis ? 1000 : 2000;\n                        setTimeout(()=>{\n                            handleAutoContinuation();\n                        }, delay);\n                    } else {\n                        conversationPromise.then(async (convId)=>{\n                            if (convId) await saveMessageToDatabase(convId, finalAssistantMessage);\n                        });\n                    }\n                }\n            }\n        } catch (err) {\n            console.error(\"Playground API call error:\", err);\n            const errorMessage = {\n                id: Date.now().toString() + \"-error\",\n                role: \"error\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: err.message || \"An unexpected error occurred.\"\n                    }\n                ]\n            };\n            setMessages((prevMessages)=>[\n                    ...prevMessages,\n                    errorMessage\n                ]);\n            setError(err.message);\n            // Phase 1 Optimization: Save error message in background\n            conversationPromise.then(async (convId)=>{\n                if (convId) {\n                    console.log(\"\\uD83D\\uDCBE [PARALLEL] Saving error message in background...\");\n                    await saveMessageToDatabase(convId, errorMessage);\n                    console.log(\"✅ [PARALLEL] Error message saved\");\n                }\n            }).catch((saveErr)=>{\n                console.error(\"❌ [PARALLEL] Error message save failed:\", saveErr);\n            });\n        } finally{\n            setIsLoading(false);\n            // Mark status as complete and log performance\n            messageStatus.markComplete();\n            (0,_hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_10__.logStatusPerformance)(messageStatus.stageHistory);\n            // Phase 1 Optimization: Performance summary\n            const totalMessagingTime = performance.now() - messagingStartTime;\n            console.log(\"\\uD83D\\uDCCA [MESSAGING FLOW] Total time: \".concat(totalMessagingTime.toFixed(1), \"ms\"));\n            // Phase 1 Optimization: Refresh chat history in background, don't block UI\n            conversationPromise.then(async (convId)=>{\n                if (convId && !currentConversation) {\n                    console.log(\"\\uD83D\\uDD04 [PARALLEL] Refreshing chat history in background...\");\n                    refetchChatHistory(true);\n                    console.log(\"✅ [PARALLEL] Chat history refreshed\");\n                }\n            }).catch((refreshErr)=>{\n                console.error(\"❌ [PARALLEL] Chat history refresh failed:\", refreshErr);\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col transition-all duration-300 ease-in-out\",\n                style: {\n                    marginLeft: sidebarWidth,\n                    marginRight: isCanvasOpen && !isCanvasMinimized ? \"50%\" // Canvas takes 50% of screen width\n                     : isHistoryCollapsed ? \"0px\" : \"320px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isCanvasOpen && !isCanvasMinimized ? \"50%\" // Canvas takes 50% of screen width\n                             : isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: selectedConfigId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1963,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1964,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1968,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"Not Connected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1969,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1960,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: selectedConfigId,\n                                                        onChange: (e)=>handleConfigChange(e.target.value),\n                                                        disabled: customConfigs.length === 0,\n                                                        className: \"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Router\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1980,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: config.id,\n                                                                    children: config.name\n                                                                }, config.id, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 1982,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1974,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 1989,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 1988,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 1987,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1973,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 1959,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Streaming\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1997,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setUseStreaming(!useStreaming),\n                                                className: \"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm \".concat(useStreaming ? \"bg-orange-500 shadow-orange-200\" : \"bg-gray-300\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm \".concat(useStreaming ? \"translate-x-6\" : \"translate-x-1\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2004,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 1998,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 1996,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 1957,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 1956,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 1950,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col pt-20 pb-32\",\n                        children: messages.length === 0 && !currentConversation ? /* Welcome Screen - Perfectly centered with no scroll */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex items-center justify-center px-6 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mx-auto transition-all duration-300 \".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-2xl -ml-32\" : \"max-w-4xl\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                    children: \"Welcome to RoKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2025,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600 max-w-md mx-auto\",\n                                                    children: \"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2026,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2024,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 w-full max-w-2xl\",\n                                            children: conversationStarters.map((starter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleStarterClick(starter.prompt),\n                                                    disabled: !selectedConfigId,\n                                                    className: \"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed \".concat(!selectedConfigId ? \"cursor-not-allowed\" : \"cursor-pointer hover:scale-[1.02]\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-xl flex items-center justify-center text-xl \".concat(starter.color, \" group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: starter.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2043,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors\",\n                                                                            children: starter.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2047,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                            children: starter.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2050,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2046,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2042,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-orange-500\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 4l8 8-8 8M4 12h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2057,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2056,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2055,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, starter.id, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2034,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2032,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2023,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2020,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2019,\n                            columnNumber: 13\n                        }, this) : /* Chat Messages - Scrollable area with perfect centering */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative \".concat(isCanvasOpen && !isCanvasMinimized ? \"overflow-visible\" : \"overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex \".concat(isCanvasOpen && !isCanvasMinimized ? \"justify-start\" : \"justify-center\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesContainerRef,\n                                        className: \"w-full h-full overflow-y-auto px-6 transition-all duration-300 \".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-2xl -ml-32\" : \"max-w-4xl\"),\n                                        onScroll: handleScroll,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 py-8\",\n                                            children: [\n                                                currentConversation && messages.length >= 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>loadConversation(currentConversation, true),\n                                                        disabled: isLoadingHistory,\n                                                        className: \"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50\",\n                                                        children: isLoadingHistory ? \"Loading...\" : \"Load Earlier Messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2085,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2084,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isLoadingMessages && messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: Array.from({\n                                                        length: 3\n                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2100,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2103,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2104,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-4 bg-gray-200 rounded w-5/6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2105,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2102,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2101,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2099,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2097,\n                                                    columnNumber: 23\n                                                }, this),\n                                                messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\", \" group \").concat(isCanvasOpen && !isCanvasMinimized ? \"-ml-96\" : \"\", \" \").concat(msg.role === \"assistant\" && isCanvasOpen && !isCanvasMinimized ? \"ml-8\" : \"\"),\n                                                        children: [\n                                                            msg.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-orange-500\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2125,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2124,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2123,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-[80%]\" : \"max-w-[65%]\", \" relative \").concat(msg.role === \"user\" ? \"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm\" : msg.role === \"assistant\" ? \"card text-gray-900 rounded-2xl rounded-bl-lg\" : msg.role === \"system\" ? \"bg-amber-50 text-amber-800 rounded-xl border border-amber-200\" : \"bg-red-50 text-red-800 rounded-xl border border-red-200\", \" px-4 py-3 transition-all duration-300\"),\n                                                                children: [\n                                                                    msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\",\n                                                                                className: \"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2145,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditingMessage(msg.id, msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\")),\n                                                                                className: \"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer\",\n                                                                                title: \"Edit message\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PencilSquareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"w-4 h-4 stroke-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2157,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2152,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2144,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    msg.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-8 left-0 z-10 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                text: msg.content.filter((part)=>part.type === \"text\").map((part)=>part.text).join(\"\\n\"),\n                                                                                variant: \"message\",\n                                                                                size: \"sm\",\n                                                                                title: \"Copy message\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2165,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            msg.role === \"assistant\" && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RetryDropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                configId: selectedConfigId,\n                                                                                onRetry: (apiKeyId)=>handleRetryMessage(index, apiKeyId),\n                                                                                disabled: isLoading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2172,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2164,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2 chat-message-content\",\n                                                                        children: msg.role === \"user\" && editingMessageId === msg.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: editingText,\n                                                                                    onChange: (e)=>setEditingText(e.target.value),\n                                                                                    className: \"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none\",\n                                                                                    placeholder: \"Edit your message...\",\n                                                                                    rows: 3,\n                                                                                    autoFocus: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2185,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: saveEditedMessage,\n                                                                                            disabled: !editingText.trim(),\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2199,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Save & Continue\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2200,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2194,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: cancelEditingMessage,\n                                                                                            className: \"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2206,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: \"Cancel\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                                    lineNumber: 2207,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                            lineNumber: 2202,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2193,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-white/70 text-xs\",\n                                                                                    children: \"\\uD83D\\uDCA1 Saving will restart the conversation from this point, removing all messages that came after.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2210,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                            lineNumber: 2184,\n                                                                            columnNumber: 23\n                                                                        }, this) : /* Normal message display */ msg.content.map((part, partIndex)=>{\n                                                                            if (part.type === \"text\") {\n                                                                                // Use LazyMarkdownRenderer for assistant messages, plain text for others\n                                                                                if (msg.role === \"assistant\") {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LazyMarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                        content: part.text,\n                                                                                        className: \"text-sm\"\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2221,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                } else {\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"whitespace-pre-wrap break-words leading-relaxed text-sm\",\n                                                                                        children: part.text\n                                                                                    }, partIndex, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                        lineNumber: 2229,\n                                                                                        columnNumber: 31\n                                                                                    }, this);\n                                                                                }\n                                                                            }\n                                                                            if (part.type === \"image_url\") {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                    src: part.image_url.url,\n                                                                                    alt: \"uploaded content\",\n                                                                                    className: \"max-w-full max-h-48 rounded-xl shadow-sm\"\n                                                                                }, partIndex, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2237,\n                                                                                    columnNumber: 29\n                                                                                }, this);\n                                                                            }\n                                                                            return null;\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2181,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2130,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            msg.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3.5 h-3.5 text-white\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2254,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2253,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2252,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, msg.id, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2114,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                showOrchestration && orchestrationExecutionId && isCanvasMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MinimizedCanvasCard__WEBPACK_IMPORTED_MODULE_7__.MinimizedCanvasCard, {\n                                                        orchestrationComplete: orchestrationComplete,\n                                                        onMaximize: ()=>{\n                                                            console.log(\"\\uD83C\\uDFAD [DEBUG] Maximizing canvas from minimized card\");\n                                                            // Trigger the maximize in the OrchestrationCanvas component\n                                                            setTriggerMaximize(true);\n                                                            // Reset the trigger after a brief delay\n                                                            setTimeout(()=>setTriggerMaximize(false), 100);\n                                                        },\n                                                        isCanvasOpen: isCanvasOpen,\n                                                        isCanvasMinimized: isCanvasMinimized\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2264,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2263,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicStatusIndicator__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    currentStage: messageStatus.currentStage,\n                                                    isStreaming: useStreaming && messageStatus.currentStage === \"typing\",\n                                                    orchestrationStatus: orchestrationStatus,\n                                                    onStageChange: (stage)=>{\n                                                        console.log(\"\\uD83C\\uDFAF UI Status changed to: \".concat(stage));\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showOrchestration && orchestrationExecutionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OrchestrationCanvas__WEBPACK_IMPORTED_MODULE_6__.OrchestrationCanvas, {\n                                                    executionId: orchestrationExecutionId,\n                                                    onCanvasStateChange: handleCanvasStateChange,\n                                                    forceMaximize: triggerMaximize,\n                                                    onComplete: (result)=>{\n                                                        // Skip auto-completion for test execution IDs\n                                                        if (orchestrationExecutionId === null || orchestrationExecutionId === void 0 ? void 0 : orchestrationExecutionId.startsWith(\"test-execution-id\")) {\n                                                            console.log(\"\\uD83C\\uDFAD [DEBUG] Skipping auto-completion for test execution\");\n                                                            setOrchestrationComplete(true); // Still mark as complete for UI\n                                                            return;\n                                                        }\n                                                        console.log(\"\\uD83C\\uDF89 [ORCHESTRATION] Completed:\", result);\n                                                        setOrchestrationComplete(true);\n                                                        // Add the final result as a message\n                                                        const finalMessage = {\n                                                            id: Date.now().toString() + \"-orchestration-final\",\n                                                            role: \"assistant\",\n                                                            content: [\n                                                                {\n                                                                    type: \"text\",\n                                                                    text: result\n                                                                }\n                                                            ]\n                                                        };\n                                                        setMessages((prevMessages)=>[\n                                                                ...prevMessages,\n                                                                finalMessage\n                                                            ]);\n                                                        // Hide orchestration UI\n                                                        setShowOrchestration(false);\n                                                        setOrchestrationExecutionId(null);\n                                                        setOrchestrationComplete(false); // Reset for next orchestration\n                                                        // Save final message\n                                                        if (currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) {\n                                                            saveMessageToDatabase(currentConversation.id, finalMessage).catch((err)=>{\n                                                                console.error(\"❌ Failed to save orchestration final message:\", err);\n                                                            });\n                                                        }\n                                                    },\n                                                    onError: (error)=>{\n                                                        // Skip auto-close for test execution IDs\n                                                        if (orchestrationExecutionId === null || orchestrationExecutionId === void 0 ? void 0 : orchestrationExecutionId.startsWith(\"test-execution-id\")) {\n                                                            console.log(\"\\uD83C\\uDFAD [DEBUG] Ignoring test execution error:\", error);\n                                                            return;\n                                                        }\n                                                        console.error(\"❌ [ORCHESTRATION] Error:\", error);\n                                                        setError(\"Orchestration error: \".concat(error));\n                                                        setShowOrchestration(false);\n                                                        setOrchestrationExecutionId(null);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2292,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: messagesEndRef\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2343,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2081,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2074,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2071,\n                                    columnNumber: 15\n                                }, this),\n                                showScrollToBottom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToBottom(true),\n                                        className: \"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group\",\n                                        \"aria-label\": \"Scroll to bottom\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2357,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2356,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2351,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2350,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2068,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2016,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 z-50 transition-all duration-300 ease-in-out\",\n                        style: {\n                            left: sidebarWidth,\n                            right: isCanvasOpen && !isCanvasMinimized ? \"50%\" // Canvas takes 50% of screen width\n                             : isHistoryCollapsed ? \"0px\" : \"320px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 pt-3 pb-2 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full transition-all duration-300 \".concat(isCanvasOpen && !isCanvasMinimized ? \"max-w-2xl\" : \"max-w-4xl\"),\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-red-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2382,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2381,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-800 text-sm font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2384,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2380,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2379,\n                                        columnNumber: 17\n                                    }, this),\n                                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 space-x-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                console.log(\"\\uD83C\\uDFAD [DEBUG] Manual canvas test triggered\");\n                                                console.log(\"\\uD83C\\uDFAD [DEBUG] Current state before:\", {\n                                                    showOrchestration,\n                                                    orchestrationExecutionId\n                                                });\n                                                setOrchestrationExecutionId(\"test-execution-id-\" + Date.now());\n                                                setShowOrchestration(true);\n                                                setOrchestrationComplete(false); // Reset completion state\n                                                console.log(\"\\uD83C\\uDFAD [DEBUG] State should be updated now\");\n                                                // Also test if OrchestrationCanvas component exists\n                                                console.log(\"\\uD83C\\uDFAD [DEBUG] OrchestrationCanvas component:\", _components_OrchestrationCanvas__WEBPACK_IMPORTED_MODULE_6__.OrchestrationCanvas);\n                                            },\n                                            className: \"px-4 py-2 bg-purple-500 text-white rounded-lg text-sm hover:bg-purple-600\",\n                                            children: \"\\uD83C\\uDFAD Test Canvas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2392,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2391,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSendMessage,\n                                        children: [\n                                            imagePreviews.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2418,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: [\n                                                                            imagePreviews.length,\n                                                                            \" image\",\n                                                                            imagePreviews.length > 1 ? \"s\" : \"\",\n                                                                            \" attached\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2419,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2417,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>handleRemoveImage(),\n                                                                className: \"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium\",\n                                                                children: \"Clear all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2423,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2416,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3\",\n                                                        children: imagePreviews.map((preview, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: preview,\n                                                                                alt: \"Preview \".concat(index + 1),\n                                                                                className: \"w-full h-full object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2435,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2440,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>handleRemoveImage(index),\n                                                                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100\",\n                                                                                \"aria-label\": \"Remove image \".concat(index + 1),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-3.5 h-3.5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                    lineNumber: 2447,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 2441,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2434,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 2450,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2433,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 2431,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2415,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-4 space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            multiple: true,\n                                                            onChange: handleImageChange,\n                                                            ref: fileInputRef,\n                                                            className: \"hidden\",\n                                                            id: \"imageUpload\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2463,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            disabled: imageFiles.length >= 10,\n                                                            className: \"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 \".concat(imageFiles.length >= 10 ? \"text-gray-300 cursor-not-allowed\" : \"text-gray-400 hover:text-orange-500 hover:bg-orange-50\"),\n                                                            \"aria-label\": imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images\",\n                                                            title: imageFiles.length >= 10 ? \"Maximum 10 images reached\" : \"Attach images (up to 10)\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2486,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                imageFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold\",\n                                                                    children: imageFiles.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2488,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2474,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: messageInput,\n                                                                onChange: (e)=>setMessageInput(e.target.value),\n                                                                placeholder: selectedConfigId ? \"Type a message...\" : \"Select a router first\",\n                                                                disabled: !selectedConfigId || isLoading,\n                                                                rows: 1,\n                                                                className: \"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                        e.preventDefault();\n                                                                        if ((messageInput.trim() || imageFiles.length > 0) && selectedConfigId && !isLoading) {\n                                                                            handleSendMessage();\n                                                                        }\n                                                                    }\n                                                                },\n                                                                style: {\n                                                                    minHeight: \"24px\",\n                                                                    maxHeight: \"120px\"\n                                                                },\n                                                                onInput: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.height = \"auto\";\n                                                                    target.style.height = Math.min(target.scrollHeight, 120) + \"px\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2496,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2495,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: !selectedConfigId || isLoading || !messageInput.trim() && imageFiles.length === 0,\n                                                            className: \"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0\",\n                                                            \"aria-label\": \"Send message\",\n                                                            title: \"Send message\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 animate-spin\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 2530,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2529,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_PaperClipIcon_TrashIcon_XCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 2533,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 2521,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2461,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2374,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2373,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2367,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 1943,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 \".concat(isHistoryCollapsed ? \"w-0 overflow-hidden\" : \"w-80\"),\n                style: {\n                    transform: isHistoryCollapsed ? \"translateX(100%)\" : \"translateX(0)\",\n                    opacity: isHistoryCollapsed ? 0 : 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-orange-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2559,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2558,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2557,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2563,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        chatHistory.length,\n                                                        \" conversations\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 2564,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2562,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2556,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsHistoryCollapsed(!isHistoryCollapsed),\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105\",\n                                    \"aria-label\": \"Toggle history sidebar\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2573,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2572,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 2567,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2555,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startNewChat,\n                                className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2585,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2584,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"New Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2587,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2580,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2579,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n                            children: isLoadingHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 p-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-xl border border-gray-100 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-4 w-3/4 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2598,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200 h-3 w-1/2 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2599,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2597,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2595,\n                                columnNumber: 15\n                            }, this) : chatHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 2607,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2606,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2605,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No conversations yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2610,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Start chatting to see your history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2611,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2604,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: chatHistory.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatHistoryItem, {\n                                        chat: chat,\n                                        currentConversation: currentConversation,\n                                        onLoadChat: loadChatFromHistory,\n                                        onDeleteChat: deleteConversation\n                                    }, chat.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2616,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2593,\n                            columnNumber: 11\n                        }, this),\n                        isChatHistoryStale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-orange-50 border-t border-orange-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-xs text-orange-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 mr-1 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 2633,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2632,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Updating...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2631,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2630,\n                            columnNumber: 13\n                        }, this),\n                        chatHistoryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-red-50 border-t border-red-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Failed to load history\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2644,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>refetchChatHistory(true),\n                                        className: \"text-red-700 hover:text-red-800 font-medium\",\n                                        children: \"Retry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 2645,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 2643,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2642,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2553,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2547,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out \".concat(isHistoryCollapsed ? \"opacity-100 scale-100 translate-x-0\" : \"opacity-0 scale-95 translate-x-4 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsHistoryCollapsed(false),\n                    className: \"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105\",\n                    \"aria-label\": \"Show history sidebar\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 2667,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 2666,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 2661,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n                lineNumber: 2658,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 1941,\n        columnNumber: 5\n    }, this);\n}\n_s(PlaygroundPage, \"NIjNRhA7wrohJGxtVZg2ADMmU/8=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_8__.useSidebar,\n        _hooks_useMessageStatus__WEBPACK_IMPORTED_MODULE_10__.useSmartMessageStatus,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_9__.useChatHistory,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_9__.useChatHistoryPrefetch\n    ];\n});\n_c1 = PlaygroundPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatHistoryItem\");\n$RefreshReg$(_c1, \"PlaygroundPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/playground/page.tsx\n"));

/***/ })

});