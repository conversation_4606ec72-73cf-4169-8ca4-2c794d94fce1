"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useOrchestrationStream.ts":
/*!*********************************************!*\
  !*** ./src/hooks/useOrchestrationStream.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLatestOrchestrationEvent: function() { return /* binding */ useLatestOrchestrationEvent; },\n/* harmony export */   useOrchestrationEventsByType: function() { return /* binding */ useOrchestrationEventsByType; },\n/* harmony export */   useOrchestrationProgress: function() { return /* binding */ useOrchestrationProgress; },\n/* harmony export */   useOrchestrationStream: function() { return /* binding */ useOrchestrationStream; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useOrchestrationStream,useOrchestrationEventsByType,useLatestOrchestrationEvent,useOrchestrationProgress auto */ \nfunction useOrchestrationStream(executionId, directStreamUrl) {\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [lastEvent, setLastEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [streamUrl, setStreamUrl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const pollingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttempts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const maxReconnectAttempts = 5;\n    const baseReconnectDelay = 1000; // 1 second\n    // Track the current execution ID and stream URL to detect changes\n    const currentExecutionIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(\"\");\n    const currentStreamUrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(\"\");\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (eventSourceRef.current) {\n            eventSourceRef.current.close();\n            eventSourceRef.current = null;\n        }\n        if (reconnectTimeoutRef.current) {\n            clearTimeout(reconnectTimeoutRef.current);\n            reconnectTimeoutRef.current = null;\n        }\n        setIsConnected(false);\n    }, []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!executionId && !directStreamUrl) {\n            setError(\"No execution ID or direct stream URL provided\");\n            return;\n        }\n        // Determine the URL to connect to\n        const url = directStreamUrl || (executionId ? \"/api/orchestration/stream/\".concat(executionId) : \"\");\n        if (!url) {\n            setError(\"No valid stream URL could be determined\");\n            return;\n        }\n        // Skip if we're already connected to this URL\n        if (currentStreamUrlRef.current === url && isConnected) {\n            console.log(\"[Orchestration Stream] Already connected to: \".concat(url));\n            return;\n        }\n        console.log(\"\\uD83C\\uDFAD [Orchestration Stream] Connecting to: \".concat(url, \" with executionId: \").concat(executionId));\n        // Clean up existing connection\n        disconnect();\n        // Update refs\n        currentExecutionIdRef.current = executionId || \"\";\n        currentStreamUrlRef.current = url;\n        try {\n            const eventSource = new EventSource(url);\n            eventSourceRef.current = eventSource;\n            eventSource.onopen = ()=>{\n                console.log(\"[Orchestration Stream] Connected to execution \".concat(executionId));\n                setIsConnected(true);\n                setError(null);\n                reconnectAttempts.current = 0;\n            };\n            eventSource.onmessage = (event)=>{\n                try {\n                    const orchestrationEvent = JSON.parse(event.data);\n                    console.log(\"[Orchestration Stream] Received event:\", orchestrationEvent);\n                    setEvents((prev)=>[\n                            ...prev,\n                            orchestrationEvent\n                        ]);\n                    setLastEvent(orchestrationEvent);\n                    // Reset error state on successful message\n                    setError(null);\n                } catch (parseError) {\n                    console.error(\"[Orchestration Stream] Error parsing event:\", parseError);\n                    setError(\"Error parsing stream data\");\n                }\n            };\n            // Handle specific event types\n            eventSource.addEventListener(\"orchestration_started\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Orchestration started:\", data);\n            });\n            eventSource.addEventListener(\"step_started\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Step started:\", data);\n            });\n            eventSource.addEventListener(\"step_progress\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Step progress:\", data);\n            });\n            eventSource.addEventListener(\"step_completed\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Step completed:\", data);\n            });\n            eventSource.addEventListener(\"synthesis_started\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Synthesis started:\", data);\n            });\n            eventSource.addEventListener(\"orchestration_completed\", (event)=>{\n                const data = JSON.parse(event.data);\n                console.log(\"[Orchestration Stream] Orchestration completed:\", data);\n            });\n            eventSource.onerror = (event)=>{\n                console.error(\"[Orchestration Stream] Connection error:\", event);\n                setIsConnected(false);\n                // Attempt to reconnect with exponential backoff\n                if (reconnectAttempts.current < maxReconnectAttempts) {\n                    const delay = baseReconnectDelay * Math.pow(2, reconnectAttempts.current);\n                    reconnectAttempts.current++;\n                    setError(\"Connection lost. Reconnecting in \".concat(delay / 1000, \"s... (attempt \").concat(reconnectAttempts.current, \"/\").concat(maxReconnectAttempts, \")\"));\n                    reconnectTimeoutRef.current = setTimeout(()=>{\n                        console.log(\"[Orchestration Stream] Reconnecting... (attempt \".concat(reconnectAttempts.current, \")\"));\n                        connect();\n                    }, delay);\n                } else {\n                    setError(\"Connection failed after multiple attempts. Please refresh the page.\");\n                }\n            };\n        } catch (connectionError) {\n            console.error(\"[Orchestration Stream] Failed to create connection:\", connectionError);\n            setError(\"Failed to establish connection\");\n            setIsConnected(false);\n        }\n    }, [\n        executionId,\n        disconnect\n    ]);\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        reconnectAttempts.current = 0;\n        connect();\n    }, [\n        connect\n    ]);\n    // Connect on mount and when executionId or directStreamUrl changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (executionId || directStreamUrl) {\n            connect();\n        }\n        // Cleanup on unmount\n        return ()=>{\n            disconnect();\n        };\n    }, [\n        executionId,\n        directStreamUrl,\n        connect,\n        disconnect\n    ]);\n    // Handle page visibility changes to reconnect when page becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\" && !isConnected && (executionId || directStreamUrl)) {\n                console.log(\"[Orchestration Stream] Page became visible, attempting to reconnect...\");\n                reconnect();\n            }\n        };\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n        };\n    }, [\n        isConnected,\n        executionId,\n        directStreamUrl,\n        reconnect\n    ]);\n    // Handle online/offline events\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleOnline = ()=>{\n            if (!isConnected && (executionId || directStreamUrl)) {\n                console.log(\"[Orchestration Stream] Network came back online, attempting to reconnect...\");\n                reconnect();\n            }\n        };\n        const handleOffline = ()=>{\n            setError(\"Network connection lost\");\n        };\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n        };\n    }, [\n        isConnected,\n        executionId,\n        directStreamUrl,\n        reconnect\n    ]);\n    return {\n        events,\n        isConnected,\n        error,\n        lastEvent,\n        reconnect,\n        disconnect\n    };\n}\n// Helper hook for filtering events by type\nfunction useOrchestrationEventsByType(executionId, eventType) {\n    const { events } = useOrchestrationStream(executionId);\n    return events.filter((event)=>event.type === eventType);\n}\n// Helper hook for getting the latest event of a specific type\nfunction useLatestOrchestrationEvent(executionId, eventType) {\n    const { events } = useOrchestrationStream(executionId);\n    const filteredEvents = events.filter((event)=>event.type === eventType);\n    return filteredEvents.length > 0 ? filteredEvents[filteredEvents.length - 1] : null;\n}\n// Helper hook for tracking orchestration progress\nfunction useOrchestrationProgress(executionId) {\n    const { events } = useOrchestrationStream(executionId);\n    const stepStartedEvents = events.filter((e)=>e.type === \"step_started\");\n    const stepCompletedEvents = events.filter((e)=>e.type === \"step_completed\");\n    const orchestrationCompleted = events.some((e)=>e.type === \"orchestration_completed\");\n    const totalSteps = stepStartedEvents.length;\n    const completedSteps = stepCompletedEvents.length;\n    const currentStep = totalSteps > 0 ? Math.min(completedSteps + 1, totalSteps) : 0;\n    const progress = totalSteps > 0 ? completedSteps / totalSteps * 100 : 0;\n    return {\n        totalSteps,\n        completedSteps,\n        currentStep,\n        progress,\n        isComplete: orchestrationCompleted\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\n"));

/***/ })

});