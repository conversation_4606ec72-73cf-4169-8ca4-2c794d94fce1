"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationChatroom.tsx":
/*!**************************************************!*\
  !*** ./src/components/OrchestrationChatroom.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationChatroom: function() { return /* binding */ OrchestrationChatroom; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatMessage */ \"(app-pages-browser)/./src/components/ChatMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* harmony import */ var _utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/orchestrationUtils */ \"(app-pages-browser)/./src/utils/orchestrationUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationChatroom auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationChatroom = (param)=>{\n    let { executionId, events, isConnected, error, isComplete } = param;\n    _s();\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingSpecialists, setTypingSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Debug logging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDFAD [CHATROOM DEBUG] State:\", {\n            executionId,\n            eventsCount: events.length,\n            isConnected,\n            error,\n            isComplete,\n            events: events.slice(0, 3) // Show first 3 events\n        });\n    }, [\n        executionId,\n        events,\n        isConnected,\n        error,\n        isComplete\n    ]);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        chatMessages\n    ]);\n    // Add simulated interactive events for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (events.length > 0 && !chatMessages.some((msg)=>msg.type === \"clarification\")) {\n            // Add some simulated clarification events for testing\n            const simulatedEvents = [\n                ...events,\n                // Add a clarification question after the first assignment\n                {\n                    type: \"specialist_clarification\",\n                    role_id: \"game-designer\",\n                    data: {\n                        message: null\n                    },\n                    timestamp: new Date(Date.now() + 2000).toISOString()\n                }\n            ];\n            // Use simulated events for now to test the enhanced conversations\n            processEvents(simulatedEvents);\n        } else {\n            processEvents(events);\n        }\n    }, [\n        events,\n        executionId\n    ]);\n    // Convert orchestration events to chat messages\n    const processEvents = (eventsToProcess)=>{\n        const newMessages = [];\n        const currentlyTyping = new Set();\n        eventsToProcess.forEach((event, index)=>{\n            const timestamp = new Date(event.timestamp || Date.now());\n            const messageId = \"\".concat(executionId, \"-\").concat(index);\n            switch(event.type){\n                case \"orchestration_started\":\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"orchestration_started\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"moderator_introduction\":\n                    var _event_data;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.message) || \"Hello team! Let me introduce everyone...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"specialist_introduction\":\n                    var _event_data1, _event_data2;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.message) || \"Introducing \".concat((_event_data2 = event.data) === null || _event_data2 === void 0 ? void 0 : _event_data2.specialist, \" specialist...\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"moderator_assignment\":\n                    var _event_data3, _event_data4;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data3 = event.data) === null || _event_data3 === void 0 ? void 0 : _event_data3.message) || \"Assignment given to \".concat((_event_data4 = event.data) === null || _event_data4 === void 0 ? void 0 : _event_data4.target_specialist),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"task_decomposed\":\n                    var _event_data5;\n                    const steps = ((_event_data5 = event.data) === null || _event_data5 === void 0 ? void 0 : _event_data5.steps) || [];\n                    const teamIntro = steps.map((step)=>\"\\uD83E\\uDD16 @\".concat(step.roleId, \" - \").concat(step.modelName || \"AI Specialist\")).join(\"\\n\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: \"\".concat((0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"task_decomposed\"), \"\\n\\n\").concat(teamIntro, \"\\n\\nLet's begin the collaboration!\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"step_assigned\":\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateModeratorClarification)(event.role_id || \"specialist\", \"\", \"assignment\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"moderator_assignment\":\n                    var _event_data6;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: ((_event_data6 = event.data) === null || _event_data6 === void 0 ? void 0 : _event_data6.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"moderator_assignment\", event.role_id),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"specialist_acknowledgment\":\n                    var _event_data_speaker, _event_data7, _event_data8, _event_data9, _event_data10;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: ((_event_data7 = event.data) === null || _event_data7 === void 0 ? void 0 : (_event_data_speaker = _event_data7.speaker) === null || _event_data_speaker === void 0 ? void 0 : _event_data_speaker.replace(\"_\", \" \").toUpperCase()) || \"Specialist\",\n                        roleId: (_event_data8 = event.data) === null || _event_data8 === void 0 ? void 0 : _event_data8.speaker,\n                        content: ((_event_data9 = event.data) === null || _event_data9 === void 0 ? void 0 : _event_data9.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateSpecialistPersonalityMessage)(((_event_data10 = event.data) === null || _event_data10 === void 0 ? void 0 : _event_data10.speaker) || \"specialist\", \"acknowledgment\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"specialist_clarification\":\n                    var _event_data11;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: ((_event_data11 = event.data) === null || _event_data11 === void 0 ? void 0 : _event_data11.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateSpecialistPersonalityMessage)(event.role_id || \"specialist\", \"clarification\"),\n                        timestamp,\n                        type: \"clarification\"\n                    });\n                    break;\n                case \"specialist_working\":\n                    var _event_data_speaker1, _event_data12, _event_data13, _event_data14, _event_data15;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: ((_event_data12 = event.data) === null || _event_data12 === void 0 ? void 0 : (_event_data_speaker1 = _event_data12.speaker) === null || _event_data_speaker1 === void 0 ? void 0 : _event_data_speaker1.replace(\"_\", \" \").toUpperCase()) || \"Specialist\",\n                        roleId: (_event_data13 = event.data) === null || _event_data13 === void 0 ? void 0 : _event_data13.speaker,\n                        content: ((_event_data14 = event.data) === null || _event_data14 === void 0 ? void 0 : _event_data14.message) || \"\\uD83D\\uDD04 Analyzing the requirements...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    // Add to typing indicators\n                    if ((_event_data15 = event.data) === null || _event_data15 === void 0 ? void 0 : _event_data15.speaker) {\n                        currentlyTyping.add(event.data.speaker);\n                    }\n                    break;\n                case \"specialist_response\":\n                    var _event_data_speaker2, _event_data16, _event_data17, _event_data18, _event_data19;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: ((_event_data16 = event.data) === null || _event_data16 === void 0 ? void 0 : (_event_data_speaker2 = _event_data16.speaker) === null || _event_data_speaker2 === void 0 ? void 0 : _event_data_speaker2.replace(\"_\", \" \").toUpperCase()) || \"Specialist\",\n                        roleId: (_event_data17 = event.data) === null || _event_data17 === void 0 ? void 0 : _event_data17.speaker,\n                        content: ((_event_data18 = event.data) === null || _event_data18 === void 0 ? void 0 : _event_data18.message) || \"Task completed successfully!\",\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    // Remove from typing indicators\n                    if ((_event_data19 = event.data) === null || _event_data19 === void 0 ? void 0 : _event_data19.speaker) {\n                        currentlyTyping.delete(event.data.speaker);\n                    }\n                    break;\n                case \"moderator_handoff\":\n                    var _event_data20;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data20 = event.data) === null || _event_data20 === void 0 ? void 0 : _event_data20.message) || \"Handing off to next specialist...\",\n                        timestamp,\n                        type: \"handoff\"\n                    });\n                    break;\n                case \"moderator_synthesis_start\":\n                    var _event_data21;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data21 = event.data) === null || _event_data21 === void 0 ? void 0 : _event_data21.message) || \"Starting synthesis of all specialist contributions...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    currentlyTyping.add(\"moderator\");\n                    break;\n                case \"synthesis_progress\":\n                    var _event_data22;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data22 = event.data) === null || _event_data22 === void 0 ? void 0 : _event_data22.message) || \"\\uD83E\\uDDE9 Weaving together specialist insights...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"orchestration_complete\":\n                    var _event_data23;\n                    currentlyTyping.delete(\"moderator\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"AI Moderator\",\n                        content: ((_event_data23 = event.data) === null || _event_data23 === void 0 ? void 0 : _event_data23.message) || \"\\uD83C\\uDF89 Team collaboration complete!\",\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n                case \"step_started\":\n                    // Add to typing indicators\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"step_progress\":\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"specialist_message\":\n                    var _event_data24, _event_data25;\n                    const completionMessage = ((_event_data24 = event.data) === null || _event_data24 === void 0 ? void 0 : _event_data24.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateSpecialistPersonalityMessage)(event.role_id || \"specialist\", \"completion\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: \"\".concat(completionMessage, \"\\n\\n\").concat(((_event_data25 = event.data) === null || _event_data25 === void 0 ? void 0 : _event_data25.output) || \"Task completed successfully!\"),\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n                case \"step_completed\":\n                    // Remove from typing\n                    if (event.role_id) {\n                        currentlyTyping.delete(event.role_id);\n                    }\n                    break;\n                case \"handoff_message\":\n                    var _event_data26, _event_data27;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data26 = event.data) === null || _event_data26 === void 0 ? void 0 : _event_data26.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"handoff_message\", (_event_data27 = event.data) === null || _event_data27 === void 0 ? void 0 : _event_data27.fromRole),\n                        timestamp,\n                        type: \"handoff\"\n                    });\n                    break;\n                case \"synthesis_started\":\n                    var _event_data28;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data28 = event.data) === null || _event_data28 === void 0 ? void 0 : _event_data28.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"synthesis_started\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    currentlyTyping.add(\"moderator\");\n                    break;\n                case \"synthesis_complete\":\n                    var _event_data29;\n                    currentlyTyping.delete(\"moderator\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data29 = event.data) === null || _event_data29 === void 0 ? void 0 : _event_data29.message) || (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_4__.generateConversationalMessage)(\"synthesis_complete\"),\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n            }\n        });\n        setChatMessages(newMessages);\n        setTypingSpecialists(currentlyTyping);\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 361,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n            lineNumber: 360,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 text-xs font-medium \".concat(isConnected ? \"bg-green-50 text-green-700 border-b border-green-100\" : \"bg-yellow-50 text-yellow-700 border-b border-yellow-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? \"bg-green-500\" : \"bg-yellow-500\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: isConnected ? \"Connected to AI Team\" : \"Connecting...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    chatMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600 animate-pulse\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Waiting for AI team to start collaboration...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, undefined),\n                    chatMessages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from(typingSpecialists).map((specialist)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_3__.TypingIndicator, {\n                            senderName: specialist,\n                            roleId: specialist !== \"moderator\" ? specialist : undefined\n                        }, specialist, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrchestrationChatroom, \"xNWPH4WkId/aBek//nEKgqbFhaw=\");\n_c = OrchestrationChatroom;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationChatroom\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\n"));

/***/ })

});