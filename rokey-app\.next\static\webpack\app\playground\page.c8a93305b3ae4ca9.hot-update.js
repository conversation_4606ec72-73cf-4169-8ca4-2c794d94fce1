"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationCanvas.tsx":
/*!************************************************!*\
  !*** ./src/components/OrchestrationCanvas.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationCanvas: function() { return /* binding */ OrchestrationCanvas; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OrchestrationChatroom */ \"(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationCanvas = (param)=>{\n    let { executionId, onComplete, onError, onCanvasStateChange, forceMaximize = false } = param;\n    _s();\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orchestrationComplete, setOrchestrationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { events, isConnected, error } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Debug logging\n    console.log(\"\\uD83C\\uDFAD [CANVAS DEBUG] OrchestrationCanvas state:\", {\n        executionId,\n        isConnected,\n        error,\n        eventsCount: events.length,\n        events: events.map((e)=>({\n                type: e.type,\n                timestamp: e.timestamp\n            }))\n    });\n    // Handle orchestration completion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const synthesisCompleteEvent = events.find((event)=>event.type === \"synthesis_complete\");\n        if (synthesisCompleteEvent && !orchestrationComplete) {\n            var _synthesisCompleteEvent_data;\n            setOrchestrationComplete(true);\n            const result = ((_synthesisCompleteEvent_data = synthesisCompleteEvent.data) === null || _synthesisCompleteEvent_data === void 0 ? void 0 : _synthesisCompleteEvent_data.result) || \"Orchestration completed successfully\";\n            setFinalResult(result);\n            // Notify parent component\n            if (onComplete) {\n                onComplete(result);\n            }\n        }\n    }, [\n        events,\n        orchestrationComplete,\n        onComplete\n    ]);\n    // Handle errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (error && onError) {\n            onError(error);\n        }\n    }, [\n        error,\n        onError\n    ]);\n    const handleMinimize = ()=>{\n        setIsMinimized(true);\n        // Keep isCanvasOpen as true so component doesn't disappear completely\n        // We'll hide it via CSS transform instead\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(false, true);\n    };\n    const handleMaximize = ()=>{\n        setIsMinimized(false);\n        // isCanvasOpen should already be true, but ensure it\n        setIsCanvasOpen(true);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(true, false);\n    };\n    // Notify parent of initial canvas state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(isCanvasOpen, isMinimized);\n    }, [\n        isCanvasOpen,\n        isMinimized,\n        onCanvasStateChange\n    ]);\n    // Handle external maximize trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (forceMaximize && isMinimized) {\n            console.log(\"\\uD83C\\uDFAD [DEBUG] External maximize trigger received!\");\n            handleMaximize();\n        }\n    }, [\n        forceMaximize,\n        isMinimized\n    ]);\n    // Minimized card state - now returns null, will be rendered inline in chat\n    if (isMinimized) {\n        return null;\n    }\n    // Canvas is closed\n    if (!isCanvasOpen) {\n        return null;\n    }\n    // Debug log when rendering\n    console.log(\"\\uD83C\\uDFAD [DEBUG] OrchestrationCanvas is rendering!\", {\n        isCanvasOpen,\n        isMinimized,\n        executionId,\n        shouldBeVisible: isCanvasOpen && !isMinimized,\n        transformClass: isCanvasOpen && !isMinimized ? \"translate-x-0\" : \"translate-x-full\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-0 right-0 h-full w-1/2 bg-white shadow-2xl z-[9999] transform transition-transform duration-300 ease-out border-l border-gray-200 \".concat(isCanvasOpen && !isMinimized ? \"translate-x-0\" : \"translate-x-full\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-4 left-4 bg-green-500 text-white p-2 rounded z-[10000] text-xs font-bold\",\n                    children: [\n                        \"SPLIT-SCREEN CANVAS - isOpen: \",\n                        isCanvasOpen.toString(),\n                        \", isMinimized: \",\n                        isMinimized.toString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"AI Team Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: orchestrationComplete ? \"Orchestration Complete\" : \"Multi-Role Orchestration in Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleMinimize,\n                                className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200\",\n                                \"aria-label\": \"Minimize canvas\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 h-full overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__.OrchestrationChatroom, {\n                        executionId: executionId,\n                        events: events,\n                        isConnected: isConnected,\n                        error: error,\n                        isComplete: orchestrationComplete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(OrchestrationCanvas, \"KsTXJg8wJUpwnTOsKVHiLc1u04Q=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = OrchestrationCanvas;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\n"));

/***/ })

});