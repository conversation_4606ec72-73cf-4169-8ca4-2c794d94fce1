'use client';

import React from 'react';
import { 
  UserIcon,
  CpuChipIcon,
  SparklesIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

interface ChatMessageData {
  id: string;
  sender: 'moderator' | 'specialist';
  senderName: string;
  roleId?: string;
  content: string;
  timestamp: Date;
  type: 'message' | 'assignment' | 'handoff' | 'clarification' | 'completion';
}

interface ChatMessageProps {
  message: ChatMessageData;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const getRoleColor = (roleId?: string) => {
    if (!roleId) return 'from-blue-500 to-blue-600'; // Moderator
    
    // Generate consistent colors based on role name
    const colors = [
      'from-green-500 to-green-600',
      'from-purple-500 to-purple-600',
      'from-orange-500 to-orange-600',
      'from-pink-500 to-pink-600',
      'from-indigo-500 to-indigo-600',
      'from-teal-500 to-teal-600',
      'from-red-500 to-red-600',
      'from-yellow-500 to-yellow-600',
    ];
    
    const hash = roleId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const getRoleIcon = (sender: string, roleId?: string) => {
    if (sender === 'moderator') {
      return <SparklesIcon className="w-4 h-4" />;
    }
    return <CpuChipIcon className="w-4 h-4" />;
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'assignment':
        return <ArrowRightIcon className="w-3 h-3 text-blue-500" />;
      case 'completion':
        return <CheckCircleIcon className="w-3 h-3 text-green-500" />;
      case 'handoff':
        return <ArrowRightIcon className="w-3 h-3 text-purple-500" />;
      case 'clarification':
        return <svg className="w-3 h-3 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>;
      default:
        return null;
    }
  };

  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const isFromModerator = message.sender === 'moderator';
  const roleColor = getRoleColor(message.roleId);

  return (
    <div className={`flex ${isFromModerator ? 'justify-start' : 'justify-start'} mb-4`}>
      <div className="flex items-start space-x-3 max-w-[85%]">
        {/* Avatar */}
        <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ${roleColor} flex items-center justify-center text-white shadow-sm`}>
          {getRoleIcon(message.sender, message.roleId)}
        </div>

        {/* Message Content */}
        <div className="flex-1 min-w-0">
          {/* Sender Info */}
          <div className="flex items-center space-x-2 mb-1">
            <span className={`text-sm font-semibold ${
              isFromModerator ? 'text-blue-700' : 'text-gray-700'
            }`}>
              {message.senderName}
            </span>
            
            {getMessageTypeIcon(message.type) && (
              <div className="flex items-center">
                {getMessageTypeIcon(message.type)}
              </div>
            )}
            
            <span className="text-xs text-gray-500">
              {formatTime(message.timestamp)}
            </span>
          </div>

          {/* Message Bubble */}
          <div className={`inline-block px-4 py-3 rounded-2xl shadow-sm ${
            isFromModerator 
              ? 'bg-blue-50 border border-blue-100' 
              : 'bg-gray-50 border border-gray-100'
          } ${
            message.type === 'completion' ? 'border-green-200 bg-green-50' :
            message.type === 'assignment' ? 'border-blue-200 bg-blue-50' :
            message.type === 'handoff' ? 'border-purple-200 bg-purple-50' :
            message.type === 'clarification' ? 'border-yellow-200 bg-yellow-50' : ''
          }`}>
            <div className={`text-sm leading-relaxed ${
              isFromModerator ? 'text-blue-900' : 'text-gray-800'
            } ${
              message.type === 'completion' ? 'text-green-900' :
              message.type === 'assignment' ? 'text-blue-900' :
              message.type === 'handoff' ? 'text-purple-900' :
              message.type === 'clarification' ? 'text-yellow-900' : ''
            }`}>
              {/* Format message content with line breaks */}
              {message.content.split('\n').map((line, index) => (
                <React.Fragment key={index}>
                  {line}
                  {index < message.content.split('\n').length - 1 && <br />}
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* Message Type Badge */}
          {message.type !== 'message' && (
            <div className="mt-2">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                message.type === 'assignment' ? 'bg-blue-100 text-blue-800' :
                message.type === 'completion' ? 'bg-green-100 text-green-800' :
                message.type === 'handoff' ? 'bg-purple-100 text-purple-800' :
                message.type === 'clarification' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {message.type === 'assignment' && '📋 Task Assignment'}
                {message.type === 'completion' && '✅ Task Complete'}
                {message.type === 'handoff' && '🔄 Handoff'}
                {message.type === 'clarification' && '❓ Question'}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
