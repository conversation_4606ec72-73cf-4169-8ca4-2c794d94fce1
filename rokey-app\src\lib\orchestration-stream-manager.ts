// Shared orchestration stream manager
// This ensures all API routes use the same activeStreams Map

export interface OrchestrationEvent {
  id: string;
  execution_id: string;
  type: string;
  timestamp: string;
  data: any;
  step_number?: number;
  role_id?: string;
  model_name?: string;
}

interface StreamInfo {
  controller: ReadableStreamDefaultController;
  lastEventId: string;
  startTime: number;
  heartbeatInterval?: NodeJS.Timeout;
}

// Global map to track active streams - SHARED ACROSS ALL API ROUTES
// Use globalThis to ensure it persists across Next.js module instances
declare global {
  var __orchestrationStreams: Map<string, StreamInfo> | undefined;
}

const activeStreams = globalThis.__orchestrationStreams ?? (globalThis.__orchestrationStreams = new Map<string, StreamInfo>());

// Debug: Log when this module is loaded
console.log(`🎭 [STREAM MANAGER] Module loaded - activeStreams instance:`, activeStreams.constructor.name, `size: ${activeStreams.size}`);

// Add a stream to the active streams
export function addActiveStream(executionId: string, streamInfo: Omit<StreamInfo, 'heartbeatInterval'>): void {
  activeStreams.set(executionId, streamInfo);
  console.log(`🎭 [STREAM MANAGER] Added stream for execution ${executionId}`);
  console.log(`🎭 [STREAM MANAGER] Total active streams: ${activeStreams.size}`);
}

// Remove a stream from active streams
export function removeActiveStream(executionId: string): void {
  const streamInfo = activeStreams.get(executionId);
  if (streamInfo?.heartbeatInterval) {
    clearInterval(streamInfo.heartbeatInterval);
  }
  
  const wasRemoved = activeStreams.delete(executionId);
  console.log(`🎭 [STREAM MANAGER] Removed stream for execution ${executionId}: ${wasRemoved}`);
  console.log(`🎭 [STREAM MANAGER] Total active streams: ${activeStreams.size}`);
}

// Set heartbeat interval for a stream
export function setStreamHeartbeat(executionId: string, interval: NodeJS.Timeout): void {
  const streamInfo = activeStreams.get(executionId);
  if (streamInfo) {
    streamInfo.heartbeatInterval = interval;
    console.log(`🎭 [STREAM MANAGER] Set heartbeat for execution ${executionId}`);
  }
}

// Get active stream info
export function getActiveStream(executionId: string): StreamInfo | undefined {
  return activeStreams.get(executionId);
}

// Get all active stream IDs
export function getActiveStreamIds(): string[] {
  return Array.from(activeStreams.keys());
}

// Get active stream count
export function getActiveStreamCount(): number {
  return activeStreams.size;
}

// Format event for Server-Sent Events
export function formatSSEEvent(event: OrchestrationEvent): string {
  return `id: ${event.id}\nevent: ${event.type}\ndata: ${JSON.stringify(event)}\n\n`;
}

// Broadcast event to all connected clients for an execution
export function broadcastOrchestrationEvent(
  executionId: string,
  event: OrchestrationEvent
): void {
  console.log(`🎭 [STREAM MANAGER] Attempting to broadcast ${event.type} to execution ${executionId}`);
  console.log(`🎭 [STREAM MANAGER] activeStreams instance:`, activeStreams.constructor.name);
  console.log(`🎭 [STREAM MANAGER] activeStreams size:`, activeStreams.size);
  console.log(`🎭 [STREAM MANAGER] Active streams:`, getActiveStreamIds());
  console.log(`🎭 [STREAM MANAGER] globalThis.__orchestrationStreams size:`, globalThis.__orchestrationStreams?.size);

  const streamInfo = activeStreams.get(executionId);

  if (streamInfo) {
    try {
      const encoder = new TextEncoder();
      const eventData = formatSSEEvent(event);
      streamInfo.controller.enqueue(encoder.encode(eventData));
      streamInfo.lastEventId = event.id;

      console.log(`🎭 [STREAM MANAGER] Successfully broadcasted ${event.type} to execution ${executionId}`);
      console.log(`🎭 [STREAM MANAGER] Event data:`, event);
    } catch (error) {
      console.error(`🎭 [STREAM MANAGER] Error broadcasting event: ${error}`);
      console.error(`🎭 [STREAM MANAGER] Stream may be closed or corrupted`);

      // Clean up heartbeat interval
      if (streamInfo.heartbeatInterval) {
        clearInterval(streamInfo.heartbeatInterval);
      }

      // Remove dead stream
      activeStreams.delete(executionId);
      console.log(`🎭 [STREAM MANAGER] Removed dead stream for execution ${executionId}`);
    }
  } else {
    console.warn(`🎭 [STREAM MANAGER] No active stream found for execution ${executionId}`);
    console.warn(`🎭 [STREAM MANAGER] Available streams:`, getActiveStreamIds());
    console.warn(`🎭 [STREAM MANAGER] This could indicate a timing issue or connection loss`);
  }
}

// Clean up old streams (called periodically)
export function cleanupOldStreams(maxAgeMs: number = 30 * 60 * 1000): void {
  const now = Date.now();
  
  for (const [executionId, streamInfo] of activeStreams.entries()) {
    if (now - streamInfo.startTime > maxAgeMs) {
      try {
        streamInfo.controller.close();
      } catch (error) {
        console.warn(`[STREAM MANAGER] Error closing old stream: ${error}`);
      }
      
      if (streamInfo.heartbeatInterval) {
        clearInterval(streamInfo.heartbeatInterval);
      }
      
      activeStreams.delete(executionId);
      console.log(`[STREAM MANAGER] Cleaned up old stream for execution ${executionId}`);
    }
  }
}
